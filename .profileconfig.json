{"jfrConfig": {"settings": "profile"}, "asyncProfilerConfig": {"jfrsync": true, "alloc": true, "event": "wall", "misc": ""}, "file": "$PROJECT_DIR/profile.jfr", "conversionConfig": {"nonProjectPackagePrefixes": ["java.", "javax.", "kotlin.", "jdk.", "com.google.", "org.apache.", "org.spring.", "sun.", "scala."], "enableMarkers": true, "initialVisibleThreads": 10, "initialSelectedThreads": 10, "includeGCThreads": false, "includeInitialSystemProperty": false, "includeInitialEnvironmentVariables": false, "includeSystemProcesses": false, "ignoredEvents": ["jdk.ActiveSetting", "jdk.ActiveRecording", "jdk.BooleanFlag", "jdk.IntFlag", "jdk.DoubleFlag", "jdk.LongFlag", "jdk.NativeLibrary", "jdk.StringFlag", "jdk.UnsignedIntFlag", "jdk.UnsignedLongFlag", "jdk.InitialSystemProperty", "jdk.InitialEnvironmentVariable", "jdk.SystemProcess", "jdk.ModuleExport", "jdk.ModuleRequire"], "minRequiredItemsPerThread": 3}, "additionalGradleTargets": [{"targetPrefix": "quarkus", "optionForVmArgs": "-Djvm.args", "description": "Example quarkus config, adding profiling arguments via -Djvm.args option to the Gradle task run"}], "additionalMavenTargets": [{"targetPrefix": "quarkus:", "optionForVmArgs": "-Djvm.args", "description": "Example quarkus config, adding profiling arguments via -Djvm.args option to the <PERSON><PERSON> goal run"}]}