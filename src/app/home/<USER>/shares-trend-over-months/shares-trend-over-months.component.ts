/** Angular Imports */
import { Component, OnInit } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';

/** rxjs Imports */
import { merge, forkJoin } from 'rxjs';
import { skip } from 'rxjs/operators';

/** Custom Services */
import { HomeService } from '../../home.service';

/** Charting Imports */
import Chart from 'chart.js';
import { Dates } from 'app/core/utils/dates';

/**
 * Shares Trends Bar Chart Component.
 */
@Component({
  selector: 'shares-trend-over-months',
  templateUrl: './shares-trend-over-months.component.html',
  styleUrls: ['./shares-trend-over-months.component.scss']
})
export class SharesTrendOverMonthsComponent implements OnInit {

  /** Static Form control for office Id */
  officeId = new UntypedFormControl();
  /** Static Form control for time scale */
  timescale = new UntypedFormControl();
  /** Office Data */
  officeData: any;
  /** Chart.js chart */
  chart: any;
  /** Substitute for resolver */
  hideOutput = true;

  /**
   * Fetches offices data from `resolve`
   * @param {HomeService} homeService Home Service
   * @param {ActivatedRoute} route Activated Route
   * @param {Dates} dateUtils Date Utils
   */
  constructor(private homeService: HomeService,
              private route: ActivatedRoute,
              private dateUtils: Dates) {
    this.route.data.subscribe( (data: { offices: any }) => {
      this.officeData = data.offices;
    });
  }

  ngOnInit() {
    this.getChartData();
    this.initializeControls();
  }

  /**
   * Initialize the form controls for better UX.
   */
  initializeControls() {
    this.officeId.patchValue(1);
    this.timescale.patchValue('Total');
  }

  /**
   * Subscribes to value changes of officeID and timescale controls,
   * Fetches data accordingly and sets charts based on fetched data.
   */
  getChartData() {
    merge(this.officeId.valueChanges, this.timescale.valueChanges).pipe(skip(1))
      .subscribe(() => {
        const officeId = this.officeId.value;
        const timescale = this.timescale.value;
        switch (timescale) {
          case 'Total':
            const totalSharesByMonth = this.homeService.getSharesTotalOverMonths(officeId);
            forkJoin([totalSharesByMonth]).subscribe((data: any[]) => {
              const monthLabels = this.getLabels(timescale);
              const totalShares = this.getCounts(data[0], monthLabels, timescale, 'saving');
              this.setChart(monthLabels, totalShares);
              this.hideOutput = false;
            });
            break;
            case 'Monthly':
            const sharesByMonth = this.homeService.getSharesByMonth(officeId);
            forkJoin([sharesByMonth]).subscribe((data: any[]) => {
              const monthLabels = this.getLabels(timescale);
              const sharesByMonth = this.getCounts(data[0], monthLabels, timescale, 'saving');
              this.setChart(monthLabels, sharesByMonth);
              this.hideOutput = false;
            });
            break;
        }
    });
  }

  /**
   * Gets Abscissa Labels.
   * @param {string} timescale User's timescale choice.
   */
  getLabels(timescale: string) {
    let date = new Date();
    const labelsArray = [];
    let cnt = 0;

    while (labelsArray.length < 12) {
      const transformedDate = this.dateUtils.formatDate(date, 'YY-MM');
      labelsArray.push(transformedDate);
      if (date.getMonth() === 0) {
        date.setMonth(11);
        date.setFullYear(date.getFullYear() - 1);
      } else {
        date.setMonth(date.getMonth() - 1);
      }
      date.setDate(27);

    }
    return labelsArray.reverse();
  }

  /**
   * Get bar heights for shares/loans trends.
   * @param {any[]} response API response array.
   * @param {any[]} labels Abscissa Labels.
   * @param {string} timescale User's timescale choice.
   * @param {string} type 'saving' or 'loan'.
   */
  getCounts(response: any[], labels: any[], timescale: string, type: string) {
    let counts: number[]  = [];

    labels.forEach((label: any) => {
      const month = response.find((entry: any) => {
        return entry.Months === label;
      });
      counts = this.updateCount(month, counts, type);
    });
    return counts;
  }

  /**
   * Updates the counts array.
   * @param {any} span Time span.
   * @param {any[]} counts Counts.
   * @param {string} type 'saving' or 'loan'
   */
  updateCount(span: any, counts: any[], type: string) {
    if (span) {
      switch (type) {
        case 'saving':
          counts.push(span.count);
        break;
      }
    } else {
      counts.push(0);
    }
    return counts;
  }

  /**
   * Creates an instance of Chart.js multi-bar chart.
   * Refer: https://www.chartjs.org/docs/latest/charts/bar.html for configuration details.
   * @param {any[]} labels Abscissa Labels.
   * @param {number[]} totalShares Shares Ordinate.

   */
  setChart(labels: any[], totalShares: number[]) {
    if (!this.chart) {
      this.chart = new Chart('shares-trend-over-months', {
        type: 'line',
        data: {
          labels: labels,
          datasets: [
            {
              label: 'Total Shares',
              backgroundColor: '#29B6F6',
              borderColor: '#4FC3F7',
              data: totalShares,
              fill: false
            }
          ]
        },
        options: {
          responsive: true,
          layout: {
            padding: {
              top: 5,
              left: 10,
              right: 10
            }
          },
          // plugins: {
          //   legend: false
          // },
          scales: {
            x: {
              stacked: true,
            },
            y: {
              stacked: true
            }
          }
        }
      });
    } else {
      this.chart.data.labels = labels;
      this.chart.data.datasets[0].data = totalShares;
      this.chart.update();
    }
  }

}
