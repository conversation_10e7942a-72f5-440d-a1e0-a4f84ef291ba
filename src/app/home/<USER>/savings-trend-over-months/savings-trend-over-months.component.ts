/** Angular Imports */
import { Component, OnInit } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';

/** rxjs Imports */
import { merge, forkJoin } from 'rxjs';
import { skip } from 'rxjs/operators';

/** Custom Services */
import { HomeService } from '../../home.service';

/** Charting Imports */
import Chart from 'chart.js';
import { Dates } from 'app/core/utils/dates';

/**
 * Savings Trends Bar Chart Component.
 */
@Component({
  selector: 'savings-trend-over-months',
  templateUrl: './savings-trend-over-months.component.html',
  styleUrls: ['./savings-trend-over-months.component.scss']
})
export class SavingsTrendOverMonthsComponent implements OnInit {

  /** Static Form control for office Id */
  officeId = new UntypedFormControl();
  /** Static Form control for time scale */
  timescale = new UntypedFormControl();
  /** Office Data */
  officeData: any;
  /** Chart.js chart */
  chart: any;
  /** Substitute for resolver */
  hideOutput = true;

  /**
   * Fetches offices data from `resolve`
   * @param {HomeService} homeService Home Service
   * @param {ActivatedRoute} route Activated Route
   * @param {Dates} dateUtils Date Utils
   */
  constructor(private homeService: HomeService,
              private route: ActivatedRoute,
              private dateUtils: Dates) {
    this.route.data.subscribe( (data: { offices: any }) => {
      this.officeData = data.offices;
    });
  }

  ngOnInit() {
    this.getChartData();
    this.initializeControls();
  }

  /**
   * Initialize the form controls for better UX.
   */
  initializeControls() {
    this.officeId.patchValue(1);
    this.timescale.patchValue('Total');
  }

  /**
   * Subscribes to value changes of officeID and timescale controls,
   * Fetches data accordingly and sets charts based on fetched data.
   */
  getChartData() {
    merge(this.officeId.valueChanges, this.timescale.valueChanges).pipe(skip(1))
      .subscribe(() => {
        const officeId = this.officeId.value;
        const timescale = this.timescale.value;
        switch (timescale) {
          case 'Total':
            const totalSavingsByMonth = this.homeService.getSavingsTotalOverMonths(officeId);
            const totalSysFeeByMonth = this.homeService.getSysFeeTotalOverMonths(officeId);
            const totalRegFeeByMonth = this.homeService.getRegFeeTotalOverMonths(officeId);
            forkJoin([totalSavingsByMonth, totalSysFeeByMonth, totalRegFeeByMonth]).subscribe((data: any[]) => {
              const monthLabels = this.getLabels(timescale);
              const totalSavings = this.getCounts(data[0], monthLabels, timescale, 'Savings');
              const totalSysFee = this.getCounts(data[1], monthLabels, timescale, 'System Fee');
              const totalRegFee = this.getCounts(data[2], monthLabels, timescale, 'Registration Fee');
              this.setChart(monthLabels, totalSavings, totalSysFee, totalRegFee);
              this.hideOutput = false;
            });
            break;
            case 'Monthly':
            const savingByMonth = this.homeService.getSavingsByMonth(officeId);
            const sysFeeByMonth = this.homeService.getSysFeeByMonth(officeId);
            const regFeeByMonth = this.homeService.getRegFeeByMonth(officeId);

            forkJoin([savingByMonth, sysFeeByMonth, regFeeByMonth]).subscribe((data: any[]) => {
              const monthLabels = this.getLabels(timescale);
              const savingsByMonth = this.getCounts(data[0], monthLabels, timescale, 'Savings');
              const sysFeesByMonth = this.getCounts(data[1], monthLabels, timescale, 'System Fee');
              const regFeesByMonth = this.getCounts(data[2], monthLabels, timescale, 'Registration Fee');

              this.setChart(monthLabels, savingsByMonth, sysFeesByMonth, regFeesByMonth);
              this.hideOutput = false;
            });
            break;
        }
    });
  }

  /**
   * Gets Abscissa Labels.
   * @param {string} timescale User's timescale choice.
   */
  getLabels(timescale: string) {
    let date = new Date();
    const labelsArray = [];
    let cnt = 0;

    while (labelsArray.length < 12) {
      const transformedDate = this.dateUtils.formatDate(date, 'YY-MM');
      labelsArray.push(transformedDate);
      if (date.getMonth() === 0) {
        date.setMonth(11);
        date.setFullYear(date.getFullYear() - 1);
      } else {
        date.setMonth(date.getMonth() - 1);
      }
      date.setDate(27);

    }
    return labelsArray.reverse();
  }

  /**
   * Get bar heights for savings/loans trends.
   * @param {any[]} response API response array.
   * @param {any[]} labels Abscissa Labels.
   * @param {string} timescale User's timescale choice.
   * @param {string} type 'saving' or 'loan'.
   */
  getCounts(response: any[], labels: any[], timescale: string, type: string) {
    let counts: number[]  = [];

    labels.forEach((label: any) => {
      const month = response.find((entry: any) => {
        return entry.Months === label;
      });
      counts = this.updateCount(month, counts, type);
    });
    return counts;
  }

  /**
   * Updates the counts array.
   * @param {any} span Time span.
   * @param {any[]} counts Counts.
   * @param {string} type 'saving' or 'loan'
   */
  updateCount(span: any, counts: any[], type: string) {
    if (span) {
      switch (type) {
        case 'Savings':
          counts.push(span.count);
        break;
        case 'System Fee':
          counts.push(span.count);
        break;
        case 'Registration Fee':
          counts.push(span.count);
        break;
      }
    } else {
      counts.push(0);
    }
    return counts;
  }

  /**
   * Creates an instance of Chart.js multi-bar chart.
   * Refer: https://www.chartjs.org/docs/latest/charts/bar.html for configuration details.
   * @param {any[]} labels Abscissa Labels.
   * @param {number[]} totalSavings Savings Ordinate.

   */
  setChart(labels: any[], savings: number[], sysFee: number[], regFee: number[]) {
    if (!this.chart) {
      this.chart = new Chart('savings-trend-over-months', {
        type: 'line',
        data: {
          labels: labels,
          datasets: [
            {
              label: 'Savings',
              backgroundColor: '#29B6F6',
              borderColor: '#4FC3F7',
              data: savings,
              fill: false,
              showLine: true
            },
            {
              label: 'System Fee',
              backgroundColor: '#FFCA28',
              borderColor: '#FFD54F',
              data: sysFee,
              fill: false,
              showLine: true
            },
            {
              label: 'Registration Fee',
              backgroundColor: '#EC407A',
              borderColor: '#F06292',
              data: regFee,
              fill: false,
              showLine: true
            }
          ]
        },
        options: {
          responsive: true,
          plugins: {
            legend: {
              position: 'top',
            },
            title: {
              display: true,
              text: 'Chart.js Line Chart'
            }
          }
        },
      });
    } else {
      this.chart.data.labels = labels;
      this.chart.data.datasets[0].data = savings;
      this.chart.data.datasets[1].data = sysFee;
      this.chart.data.datasets[2].data = regFee;
      this.chart.update();
    }
  }

}
