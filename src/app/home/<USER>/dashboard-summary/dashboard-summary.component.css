.card {
  width: 70rem;
  display: flex;
  flex-direction: row;
  justify-content: space-between; }

.dashboard-summary {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  /* Allows wrapping to two rows if needed */
  width: 100%;
  padding: 20px; }

.dashboard-summary-card {
  flex: 1 0 20%;
  /* Defines width as 30% of available space */
  margin: 0 10px 10px 0;
  padding: 20px;
  border-radius: 5px;
  height: 10rem; }

.summary-name {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 5px;
  text-transform: uppercase; }

.summary-value {
  font-size: 20px;
  color: #666; }

/* Customization options: */
.card {
  text-align: center; }

.dashboard-summary-card {
  background-color: #D3D3D3;
  box-shadow: 0 2px 5px rgba(141, 61, 61, 0.1); }

.summary-name {
  color: #fff;
  /* Change text color */ }

.summary-value {
  color: #fff;
  /* Change value text color */ }

/*# sourceMappingURL=dashboard-summary.component.css.map */
