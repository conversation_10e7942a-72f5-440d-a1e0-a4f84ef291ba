/** Angular Imports */
import { NgModule } from '@angular/core';

/** Custom Modules */
import { SharedModule } from '../shared/shared.module';
import { HomeRoutingModule } from './home-routing.module';
import { PipesModule } from '../pipes/pipes.module';

/** Custom Components */
import { HomeComponent } from './home.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { AmountCollectedPieComponent } from './dashboard/amount-collected-pie/amount-collected-pie.component';
import { AmountDisbursedPieComponent } from './dashboard/amount-disbursed-pie/amount-disbursed-pie.component';
import { ClientTrendsBarComponent } from './dashboard/client-trends-bar/client-trends-bar.component';
import { TranslateModule } from '@ngx-translate/core';
import { ClientsTrendOverMonthsComponent } from './dashboard/clients-trend-over-months/clients-trend-over-months.component';
import { SavingsTrendOverMonthsComponent } from './dashboard/savings-trend-over-months/savings-trend-over-months.component';
import { SavingsByProductComponent } from './dashboard/savings-by-product/savings-by-product.component';
import { SharesTrendOverMonthsComponent } from './dashboard/shares-trend-over-months/shares-trend-over-months.component';
import { DashboardSummaryComponent } from './dashboard/dashboard-summary/dashboard-summary.component';
import { ClientsClosedByReasonComponent } from './dashboard/clients-closed-by-reason/clients-closed-by-reason.component';

/**
 * Home Component
 *
 * Home and dashboard components should be declared here.
 */
@NgModule({
  imports: [
    SharedModule,
    PipesModule,
    HomeRoutingModule,
    TranslateModule.forRoot(),
  ],
  declarations: [
    HomeComponent,
    DashboardComponent,
    AmountCollectedPieComponent,
    AmountDisbursedPieComponent,
    ClientTrendsBarComponent,
    ClientsTrendOverMonthsComponent,
    SavingsTrendOverMonthsComponent,
    SavingsByProductComponent,
    SharesTrendOverMonthsComponent,
    DashboardSummaryComponent,
    ClientsClosedByReasonComponent
  ],
  providers: [ ]
})
export class HomeModule { }
