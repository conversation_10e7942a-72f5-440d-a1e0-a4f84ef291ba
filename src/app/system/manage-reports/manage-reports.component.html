<div class="container m-b-20" fxLayout="row" fxLayoutAlign="end" fxLayoutGap="20px">
  <div #buttonCreateReport class="in-block">
    <button mat-raised-button color="primary" [routerLink]="['create']" *mifosxHasPermission="'CREATE_REPORT'">
        <fa-icon icon="plus" class="m-r-10"></fa-icon>
        Create Report
    </button>
  </div>
</div>

<div class="container">

    <div #filter fxLayout="row">
        <mat-form-field fxFlex>
            <mat-label>{{ 'labels.inputs.Filter' | translate }}</mat-label>
            <input matInput (keyup)="applyFilter($event.target.value)">
        </mat-form-field>
    </div>

    <div #reportsTable class="mat-elevation-z8">

        <table mat-table [dataSource]="dataSource" matSort>

            <ng-container matColumnDef="reportName">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Report Name </th>
                <td mat-cell *matCellDef="let report"> {{ report.reportName }} </td>
            </ng-container>

            <ng-container matColumnDef="reportType">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Report Type </th>
                <td mat-cell *matCellDef="let report"> {{ report.reportType }} </td>
            </ng-container>

            <ng-container matColumnDef="reportSubType">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Report Sub Type </th>
                <td mat-cell *matCellDef="let report"> {{ report.reportSubType }} </td>
            </ng-container>

            <ng-container matColumnDef="reportCategory">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Report Category </th>
                <td mat-cell *matCellDef="let report"> {{ report.reportCategory }} </td>
            </ng-container>

            <ng-container matColumnDef="coreReport">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> Core Report </th>
                <td mat-cell *matCellDef="let report">
                    <fa-icon *ngIf="report.coreReport" icon="check-circle" size="lg" class="true" matTooltip="Yes"
                        matTooltipPosition="right"></fa-icon>
                    <fa-icon *ngIf="!report.coreReport" icon="times-circle" size="lg" class="false" matTooltip="No"
                        matTooltipPosition="right"></fa-icon>
                </td>
            </ng-container>

            <ng-container matColumnDef="userReport">
                <th mat-header-cell *matHeaderCellDef mat-sort-header> User Report </th>
                <td mat-cell *matCellDef="let report">
                    <fa-icon *ngIf="report.useReport" icon="check-circle" size="lg" class="true" matTooltip="Yes"
                        matTooltipPosition="right"></fa-icon>
                    <fa-icon *ngIf="!report.useReport" icon="times-circle" size="lg" class="false" matTooltip="No"
                        matTooltipPosition="right"></fa-icon>
                </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;" [routerLink]="[row.id]"
                class="select-row"></tr>
        </table>

        <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>

    </div>
</div>

<ng-template #templateButtonCreateReport let-popover="popover">
  <h2>Create Report</h2>
  <p class="mw300">This option allows you to create new report.</p>
  <div fxLayout="row" fxLayoutAlign="end" fxLayoutGap="2%" fxLayout.lt-md="column">
    <button mat-raised-button color="warn" (click)="popover.close();configurationWizardService.closeConfigWizard()">Close</button>
    <button mat-raised-button color="primary" (click)="popover.close();previousStep()">Back</button>
    <button mat-raised-button color="primary" (click)="popover.close();showPopover(templateFilter,filter, 'bottom', true)">Next</button>
  </div>
</ng-template>

<ng-template #templateFilter let-popover="popover">
  <h4>Search bar to filter reports by name.</h4>
  <div fxLayout="row" fxLayoutAlign="end" fxLayoutGap="2%" fxLayout.lt-md="column">
    <button mat-raised-button color="warn" (click)="popover.close();configurationWizardService.closeConfigWizard()">Close</button>
    <button mat-raised-button color="primary" (click)="popover.close();showPopover(templateButtonCreateReport, buttonCreateReport, 'bottom', true)">Back</button>
    <button mat-raised-button color="primary" (click)="popover.close();showPopover(templateReportsTable,reportsTable, 'top', true)">Next</button>
  </div>
</ng-template>

<ng-template #templateReportsTable let-popover="popover">
  <h4 class="mw300">List of all currently available reports. For more details click: <a href="https://mifosforge.jira.com/wiki/spaces/docs/pages/67895354/Manage+Reports" target="_blank">Manage Reports</a></h4>
  <div fxLayout="row" fxLayoutAlign="end" fxLayoutGap="2%" fxLayout.lt-md="column">
    <button mat-raised-button color="warn" (click)="popover.close();configurationWizardService.closeConfigWizard()">Close</button>
    <button mat-raised-button color="primary" (click)="popover.close();showPopover(templateFilter,filter, 'bottom', true)">Back</button>
    <button mat-raised-button color="primary" (click)="popover.close();nextStep()">Next</button>
  </div>
</ng-template>
