<div class="container m-b-20" fxLayout="row" fxLayoutAlign="end" fxLayoutGap="20px">

  <button mat-raised-button color="primary" [routerLink]="['edit']" *mifosxHasPermission="'UPDATE_HOOK'">
    <fa-icon icon="edit" class="m-r-10"></fa-icon>
    Edit
  </button>

  <button mat-raised-button color="warn" (click)="delete()" *mifosxHasPermission="'DELETE_HOOK'">
    <fa-icon icon="trash" class="m-r-10"></fa-icon>
    Delete
  </button>

</div>

<div class="container">

  <mat-card>

    <mat-card-content>

      <div fxLayout="row wrap" class="content">

        <div fxFlex="50%" class="mat-body-strong">
          Hook Name:
        </div>

        <div fxFlex="50%">
          {{ hookData.displayName }}
        </div>

        <div fxFlex="50%" class="mat-body-strong">
          Status:
        </div>

        <div fxFlex="50%">
          {{ hookData.isActive ? 'Active' : 'Inactive' }}
        </div>

        <div fxFlex="50%" class="mat-body-strong">
          Activation Date:
        </div>

        <div fxFlex="50%">
          {{ hookData.createdAt  | dateFormat }}
        </div>

        <div fxFlex="50%" class="mat-body-strong">
          Updated on:
        </div>

        <div fxFlex="50%">
          {{ hookData.updatedAt  | dateFormat }}
        </div>

        <div fxFlex="50%" class="mat-body-strong">
          Events:
        </div>

        <div fxFlex="50%">

          <mat-label *ngFor="let event of hookData.events">

            {{ event.actionName + '  -  ' + event.entityName }}

            <br>

          </mat-label>

        </div>

        <div fxFlex="50%" class="mat-body-strong" *ngIf="hookData.name === 'Web'">
          Content Type:
        </div>

        <div fxFlex="50%" *ngIf="hookData.name === 'Web'">
          {{ hookData.config[0].fieldValue }}
        </div>

        <div fxFlex="50%" class="mat-body-strong">
          Payload URL:
        </div>

        <div fxFlex="50%" *ngIf="hookData.name === 'Web'">
          {{ hookData.config[1].fieldValue }}
        </div>

        <div fxFlex="50%" *ngIf="hookData.name === 'SMS Bridge'">
          {{ hookData.config[0].fieldValue }}
        </div>

        <div fxFlex="50%" class="mat-body-strong" *ngIf="hookData.name === 'SMS Bridge'">
          Phone Number:
        </div>

        <div fxFlex="50%" *ngIf="hookData.name === 'SMS Bridge'">
          {{ hookData.config[1].fieldValue }}
        </div>

        <div fxFlex="50%" class="mat-body-strong" *ngIf="hookData.name === 'SMS Bridge'">
          SMS Provider:
        </div>

        <div fxFlex="50%" *ngIf="hookData.name === 'SMS Bridge'">
          {{ hookData.config[2].fieldValue }}
        </div>

        <div fxFlex="50%" class="mat-body-strong" *ngIf="hookData.name === 'SMS Bridge'">
          SMS Provider Account ID:
        </div>

        <div fxFlex="50%" *ngIf="hookData.name === 'SMS Bridge'">
          {{ hookData.config[3].fieldValue }}
        </div>

        <div fxFlex="50%" class="mat-body-strong" *ngIf="hookData.name === 'SMS Bridge'">
          SMS Provider Token:
        </div>

        <div fxFlex="50%" *ngIf="hookData.name === 'SMS Bridge'">
          {{ hookData.config[4].fieldValue }}
        </div>

      </div>

    </mat-card-content>

  </mat-card>

</div>
