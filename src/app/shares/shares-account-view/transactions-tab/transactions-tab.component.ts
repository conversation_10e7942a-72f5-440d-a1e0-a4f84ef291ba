/** Angular Imports */
import { Component, OnInit } from '@angular/core';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { SharesService } from '../../shares.service';
import { ConfirmationDialogComponent } from 'app/shared/confirmation-dialog/confirmation-dialog.component';

/**
 * Transactions Tab Component.
 */
@Component({
  selector: 'mifosx-transactions-tab',
  templateUrl: './transactions-tab.component.html',
  styleUrls: ['./transactions-tab.component.scss']
})
export class TransactionsTabComponent implements OnInit {

  /** Shares Account Data */
  shareAccountData: any;
  /** Transactions Data */
  transactionsData: any;
  /** Data source for transactions table. */
  dataSource: MatTableDataSource<any>;
  /** Columns to be displayed in transactions table. */
  displayedColumns: string[] = [
    'transactionDate',
    'transactionType',
    'totalShares',
    'purchasedOrRedeemedPrice',
    'chargeAmount',
    'amountRecievedOrReturned',
    'runningBalance',
    'actions'
  ];

  runningBalance = 0;
  processedTransactions: any[] = [];

  constructor(
    private route: ActivatedRoute,
    private sharesService: SharesService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {
    this.route.parent.data.subscribe((data: { sharesAccountData: any }) => {
      this.shareAccountData = data.sharesAccountData;
      this.transactionsData = this.shareAccountData.purchasedShares;
      this.calculateRunningBalance();
    });
  }

  ngOnInit() {
    this.dataSource = new MatTableDataSource(this.processedTransactions);
  }

  /**
   * Calculates running balance for transactions
   */
  private calculateRunningBalance() {
    this.runningBalance = 0;

    // Sort transactions by date
    const sortedTransactions = [...this.transactionsData].sort((a, b) =>
      new Date(a.purchasedDate).getTime() - new Date(b.purchasedDate).getTime()
    );

    // Calculate running balance
    this.processedTransactions = sortedTransactions.map(transaction => {
      const amount = transaction.amount || 0;

      // if(transaction.status.value === 'Approved'){
      if (transaction.type.value === 'Redeem')
        this.runningBalance -= amount;
      else if(transaction.type.value === 'Purchase')
          this.runningBalance += amount;
      // }

      return {
        ...transaction,
        runningBalance: this.runningBalance
      };
    });
  }

  /**
   * Handles the reverse transaction action
   * @param transaction The transaction to reverse
   * @param $event Mouse event
   */
  reverseTransaction(transaction: any, $event: MouseEvent) {
    $event.stopPropagation();

    console.log("Reversing transaction share: ", transaction);

    // Show confirmation dialog before proceeding with reversal
    const confirmationDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        heading: 'Reverse Transaction',
        dialogContext: `Are you sure you want to reverse the transaction with ID ${transaction.id}`,
        type: 'warn'
      }
    });

    confirmationDialogRef.afterClosed().subscribe((response: any) => {
      if (response.confirm) {
        // Call the corrected API method that properly reverses the transaction
        this.sharesService.reverseShareTransaction(transaction.accountId, transaction.id).subscribe(
          (response) => {
            this.snackBar.open("Transaction reversed successfully", "Close", {
              duration: 3000,
              verticalPosition: "top",
              horizontalPosition: "right",
            });

            // Refresh transactions data
            this.route.parent.data.subscribe((data: { sharesAccountData: any }) => {
              this.shareAccountData = data.sharesAccountData;
              this.transactionsData = this.shareAccountData.purchasedShares;
              this.calculateRunningBalance();
              this.dataSource.data = this.processedTransactions;
            });
          },
          (error) => {
            this.snackBar.open("Error reversing transaction: " + error.error.defaultUserMessage, "Close", {
              duration: 5000,
              verticalPosition: "top",
              horizontalPosition: "right",
            });
          },
        );
      }
    });
  }
}
