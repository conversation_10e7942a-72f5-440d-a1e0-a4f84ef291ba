<div class="tab-container mat-typography">

  <div class="m-b-10">
    <h3>All Transactions</h3>
  </div>

  <div class="mat-elevation-z1 m-b-25">

    <table mat-table [dataSource]="dataSource">

      <ng-container matColumnDef="transactionDate">
        <th mat-header-cell *matHeaderCellDef> Transaction Date </th>
        <td mat-cell *matCellDef="let transaction"> {{transaction.purchasedDate  | dateFormat}} </td>
      </ng-container>

      <ng-container matColumnDef="transactionType">
        <th mat-header-cell *matHeaderCellDef> Transaction Type </th>
        <td mat-cell *matCellDef="let transaction"> {{transaction.type.value}} {{transaction.type.value !== "Charge Payment" ? ('(' + transaction.status.value + ')') : ''}}</td>
      </ng-container>

      <ng-container matColumnDef="totalShares">
        <th mat-header-cell *matHeaderCellDef> Total Shares </th>
        <td mat-cell *matCellDef="let transaction"> {{transaction.numberOfShares}} </td>
      </ng-container>

      <ng-container matColumnDef="purchasedOrRedeemedPrice">
        <th mat-header-cell *matHeaderCellDef> Purhcased/Redeemed Price </th>
        <td mat-cell *matCellDef="let transaction">{{shareAccountData.currency.displaySymbol}}&nbsp;{{transaction.purchasedPrice}}</td>
      </ng-container>

      <ng-container matColumnDef="chargeAmount">
        <th mat-header-cell *matHeaderCellDef> Charge Amount </th>
        <td mat-cell *matCellDef="let transaction">{{shareAccountData.currency.displaySymbol}}&nbsp;{{transaction.type.value === "Charge Payment" ? transaction.amount : transaction.chargeAmount}}</td>
      </ng-container>

      <ng-container matColumnDef="amountRecievedOrReturned">
        <th mat-header-cell *matHeaderCellDef> Amount Recieved/Returned </th>
        <td mat-cell *matCellDef="let transaction">{{shareAccountData.currency.displaySymbol}}&nbsp;{{transaction.amount}}</td>
      </ng-container>

      <!-- running Balance column -->
      <ng-container matColumnDef="runningBalance">
        <th mat-header-cell *matHeaderCellDef> Running Balance </th>
        <td mat-cell *matCellDef="let transaction">
          {{shareAccountData.currency.displaySymbol}}&nbsp;{{transaction.runningBalance | number:'1.2-2'}}
        </td>
      </ng-container>

      <!-- Reverse button column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef> Actions </th>
        <td mat-cell *matCellDef="let transaction" >
          <button *ngIf="transaction.status.value === 'Approved' && transaction.type.value === 'Purchase' " mat-raised-button color="warn" (click)="reverseTransaction(transaction, $event)">
            <fa-icon icon="undo"></fa-icon>&nbsp;Reverse
          </button>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

    </table>

  </div>

</div>
