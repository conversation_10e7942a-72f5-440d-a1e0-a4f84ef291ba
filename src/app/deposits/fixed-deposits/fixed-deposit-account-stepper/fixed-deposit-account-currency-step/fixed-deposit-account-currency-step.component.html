<form [formGroup]="fixedDepositAccountCurrencyForm">

  <div fxLayout="row wrap" fxLayoutGap="2%" fxLayout.lt-md="column">

    <mat-form-field fxFlex="48%">
      <mat-label>Currency</mat-label>
      <input type="text" matInput formControlName="currencyCode">
    </mat-form-field>

    <mat-form-field fxFlex="48%">
      <mat-label>Decimal Places</mat-label>
      <input type="number" matInput formControlName="decimalPlaces">
    </mat-form-field>

    <mat-form-field fxFlex="48%">
      <mat-label>Currency Multiple</mat-label>
      <input type="number" matInput formControlName="currencyMultiple">
    </mat-form-field>

  </div>

  <div fxLayout="row" class="margin-t" fxLayout.xs="column" fxLayoutAlign="center" fxLayoutGap="2%">
    <button mat-raised-button matStepperPrevious>
      <fa-icon icon="arrow-left" class="m-r-10"></fa-icon>
      Previous
    </button>
    <button mat-raised-button matStepperNext>
      Next
      <fa-icon icon="arrow-right" class="m-l-10"></fa-icon>
    </button>
  </div>

</form>
