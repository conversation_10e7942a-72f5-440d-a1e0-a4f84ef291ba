<div class="tab-container mat-typography">

  <div fxLayoutAlign="start">
    <div class="m-b-20">
      <h3>All Transactions</h3>
    </div>
  </div>

  <div class="mat-elevation-z1 m-b-25">

    <table mat-table [dataSource]="dataSource" >

      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef> ID </th>
        <td mat-cell *matCellDef="let transaction"> {{ transaction.id }} </td>
      </ng-container>

      <ng-container matColumnDef="transactionDate">
        <th mat-header-cell *matHeaderCellDef> Transaction Date </th>
        <td mat-cell *matCellDef="let transaction"> {{ transaction.date  | dateFormat }} </td>
      </ng-container>

      <ng-container matColumnDef="transactionType">
        <th mat-header-cell *matHeaderCellDef> Transaction Type </th>
        <td mat-cell *matCellDef="let transaction"> {{ transaction.transactionType.value  }} </td>
      </ng-container>

      <ng-container matColumnDef="debit">
        <th mat-header-cell *matHeaderCellDef> Debit </th>
        <td mat-cell *matCellDef="let transaction"> {{ isDebit(transaction.transactionType) ? transaction.amount : 'N/A'}} </td>
      </ng-container>

      <ng-container matColumnDef="credit">
        <th mat-header-cell *matHeaderCellDef> Credit </th>
        <td mat-cell *matCellDef="let transaction"> {{ !isDebit(transaction.transactionType) ? transaction.amount : 'N/A' }} </td>
      </ng-container>

      <ng-container matColumnDef="balance">
        <th mat-header-cell *matHeaderCellDef> Balance </th>
        <td mat-cell *matCellDef="let transaction"> {{ transaction.runningBalance }} </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="select-row" (click)="showTransactions(row)"></tr>

    </table>

  </div>

</div>
