<mat-card class="fixed-deposits-account-card">

  <mat-card-header fxLayout="column" class="header">

    <mat-card-title-group class="header-title-group">

      <div class="profile-image-container">
        <div>
          <img mat-card-md-image class="profile-image"
            matTooltip="Fixed Deposits Account"
            [src]="'assets/images/fd_account_placeholder.png'">
        </div>
      </div>

      <div class="mat-typography account-card-title">
        <mat-card-title>
          <h3>
            <i class="fa fa-stop" [ngClass]="fixedDepositsAccountData.status.code | statusLookup" [matTooltip]="fixedDepositsAccountData.status.value"></i>
              Account Name : {{fixedDepositsAccountData.depositProductName}}
          </h3>
        </mat-card-title>
        <mat-card-subtitle>
          <p>
            Account #: {{fixedDepositsAccountData.accountNo}} | Client Name: {{fixedDepositsAccountData.clientName}}<br/>
            <span *ngIf="!fixedDepositsAccountData.status.rejected && !fixedDepositsAccountData.status.submittedAndPendingApproval">
              Current Balance: {{fixedDepositsAccountData.currency.displaySymbol}}&nbsp;{{fixedDepositsAccountData.summary.accountBalance}}<br/>
              Deposit Amount: {{fixedDepositsAccountData.currency.displaySymbol}}&nbsp;{{fixedDepositsAccountData.depositAmount}}<br/>
            </span>
          </p>
        </mat-card-subtitle>
      </div>

    </mat-card-title-group>

    <mat-card-actions class="account-actions">

      <ng-container *ngFor="let button of buttonConfig.singleButtons">
        <button mat-raised-button (click)="doAction(button.name)">
          <i class="{{button.icon}}"></i> {{button.name}}</button>
      </ng-container>
  
      <ng-container *ngIf="buttonConfig.options.length">
        <button mat-raised-button [matMenuTriggerFor]="More">More</button>
        <mat-menu #More="matMenu">
        <span *ngFor="let option of buttonConfig.options">
          <button mat-menu-item (click)="doAction(option.name)">{{option.name}}</button>
        </span>
        </mat-menu>
      </ng-container>
  
    </mat-card-actions>

  </mat-card-header>

  <mat-card-content class="content">

    <div class="fixed-deposits-account-tables" fxLayout="row" fxLayoutGap="2%">

      <div fxFlex="49%" fxLayout="column" fxLayoutGap="4%">

        <div>
          <h4 class="table-headers">Fixed Deposit Details</h4>
          <table>
            <tbody>
              <tr>
                <td>Activated On</td>
                <td>{{fixedDepositsAccountData.timeline.activatedOnDate ? (fixedDepositsAccountData.timeline.activatedOnDate  | dateFormat) : 'Not Activated'}}</td>
              </tr>
              <tr *ngIf="fixedDepositsAccountData.timeline.closedOnDate">
                <td>Closed On</td>
                <td>{{fixedDepositsAccountData.timeline.closedOnDate  | dateFormat}}</td>
              </tr>
              <tr>
                <td>Field Officer</td>
                <td>{{fixedDepositsAccountData.fieldOfficerName ? fixedDepositsAccountData.fieldOfficerName : 'Unassigned'}}</td>
              </tr>
              <tr>
                <td>Maturity Date</td>
                <td>{{fixedDepositsAccountData.maturityDate ? (fixedDepositsAccountData.maturityDate  | dateFormat) : 'N/A'}}</td>
              </tr>
              <tr>
                <td>Deposit Period</td>
                <td>{{fixedDepositsAccountData.depositPeriod}}&nbsp;{{fixedDepositsAccountData.depositPeriodFrequency.value}}</td>
              </tr>
              <tr *ngIf="fixedDepositsAccountData.withHoldTax">
                <td>Tax Group</td>
                <td>{{fixedDepositsAccountData.taxGroup.name}}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div>
          <h4 class="table-headers">Interest Details</h4>
          <table>
            <tbody>
              <tr>
                <td>Interest Rate</td>
                <td>{{fixedDepositsAccountData.nominalAnnualInterestRate}}%</td>
              </tr>
              <tr>
                <td>Interest Compounding Period</td>
                <td>{{fixedDepositsAccountData.interestCompoundingPeriodType.value}}</td>
              </tr>
              <tr>
                <td>Interest Posting Period</td>
                <td>{{fixedDepositsAccountData.interestPostingPeriodType.value}}</td>
              </tr>
              <tr>
                <td>Interest Calculated Using</td>
                <td>{{fixedDepositsAccountData.interestCalculationType.value}}</td>
              </tr>
              <tr>
                <td>No. of Days in Year</td>
                <td>{{fixedDepositsAccountData.interestCalculationDaysInYearType.value}}</td>
              </tr>
              <tr *ngIf="fixedDepositsAccountData.preClosurePenalApplicable">
                <td>Pre-cloure Penal</td>
                <td>{{fixedDepositsAccountData.preClosurePenalInterest}} % on {{fixedDepositsAccountData.preClosurePenalInterestOnType.value}}</td>
              </tr>
          </table>
        </div>

      </div>

      <div fxFlex="49%">

        <div *ngIf="!fixedDepositsAccountData.status.rejected && !fixedDepositsAccountData.status.submittedAndPendingApproval; else otherDetails">
          <h4 class="table-headers">Performance History</h4>
          <table>
            <tbody>
              <tr>
                <td>Principal Amount</td>
                <td>{{fixedDepositsAccountData.currency.displaySymbol}}&nbsp;{{fixedDepositsAccountData.depositAmount}}</td>
              </tr>
              <tr>
                <td>Maturity Amount</td>
                <td>{{fixedDepositsAccountData.currency.displaySymbol}}&nbsp;{{fixedDepositsAccountData.maturityAmount}}</td>
              </tr>
              <tr *ngIf="fixedDepositsAccountData.summary.totalDeposits">
                <td>Total Deposits</td>
                <td>{{fixedDepositsAccountData.currency.displaySymbol}}&nbsp;{{fixedDepositsAccountData.summary.totalDeposits}}</td>
              </tr>
              <tr *ngIf="fixedDepositsAccountData.summary.totalWithdrawals">
                <td>Total Withdrawals</td>
                <td>{{fixedDepositsAccountData.currency.displaySymbol}}&nbsp;{{fixedDepositsAccountData.summary.totalWithdrawals}}</td>
              </tr>
              <tr *ngIf="fixedDepositsAccountData.summary.totalInterestEarned >= 0">
                <td>Total Interest Earned</td>
                <td>{{fixedDepositsAccountData.currency.displaySymbol}}&nbsp;{{fixedDepositsAccountData.summary.totalInterestEarned}}</td>
              </tr>
          </table>
        </div>

        <ng-template #otherDetails>
          <div>
            <h4 class="table-headers">Other Details</h4>
            <table>
              <tbody>
                <tr>
                  <td>Principal Amount</td>
                  <td>{{fixedDepositsAccountData.currency.displaySymbol}}&nbsp;{{fixedDepositsAccountData.depositAmount}}</td>
                </tr>
                <tr>
                  <td>Maturity Amount</td>
                  <td>{{fixedDepositsAccountData.currency.displaySymbol}}&nbsp;{{fixedDepositsAccountData.maturityAmount}}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </ng-template>

      </div>

    </div>

    <nav mat-tab-nav-bar class="navigation-tabs">
      <a mat-tab-link [routerLink]="['./interest-rate-chart']" routerLinkActive #IRC="routerLinkActive"
      [active]="IRC.isActive">
        Interest Rate Chart
      </a>
      <a mat-tab-link [routerLink]="['./transactions']" routerLinkActive #transactions="routerLinkActive"
      [active]="transactions.isActive">
        Transactions
      </a>
      <a mat-tab-link [routerLink]="['./charges']" routerLinkActive #charges="routerLinkActive"
      [active]="charges.isActive">
        Charges
      </a>
      <a mat-tab-link [routerLink]="['./standing-instructions']" routerLinkActive #standingInstructions="routerLinkActive"
        [active]="standingInstructions.isActive" *ngIf="fixedDepositsAccountData.clientId">
        Standing Instructions
      </a>
      <ng-container *ngFor="let savingsDatatable of savingsDatatables">
        <a mat-tab-link *mifosxHasPermission="'READ_' + savingsDatatable.registeredTableName"
          [routerLink]="['./datatables',savingsDatatable.registeredTableName]"
          routerLinkActive #datatable="routerLinkActive" [active]="datatable.isActive">
          {{savingsDatatable.registeredTableName}}
        </a>
      </ng-container>
    </nav>

    <router-outlet></router-outlet>

  </mat-card-content>

</mat-card>
