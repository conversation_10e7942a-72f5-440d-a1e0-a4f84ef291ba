<div class="tab-container mat-typography">

  <div class="m-b-10">
    <h3>All Standing Instructions</h3>
  </div>

  <div class="mat-elevation-z1 m-b-25">

    <table mat-table #instructionsTable [dataSource]="dataSource">

      <ng-container matColumnDef="client">
        <th mat-header-cell *matHeaderCellDef> Client </th>
        <td mat-cell *matCellDef="let instruction">{{instruction.fromClient.displayName}}-{{instruction.fromClient.id}}</td>
      </ng-container>

      <ng-container matColumnDef="fromAccount">
        <th mat-header-cell *matHeaderCellDef> From Account </th>
        <td mat-cell *matCellDef="let instruction">{{instruction.fromAccount.accountNo}} ({{instruction.fromAccountType.value}})</td>
      </ng-container>

      <ng-container matColumnDef="beneficiary">
        <th mat-header-cell *matHeaderCellDef> Beneficiary </th>
        <td mat-cell *matCellDef="let instruction">{{instruction.toClient.displayName}}</td>
      </ng-container>

      <ng-container matColumnDef="toAccount">
        <th mat-header-cell *matHeaderCellDef> To Account </th>
        <td mat-cell *matCellDef="let instruction">{{instruction.toAccount.accountNo}} ({{instruction.toAccountType.value}})</td>
      </ng-container>

      <ng-container matColumnDef="amount">
        <th mat-header-cell *matHeaderCellDef> Amount </th>
        <td mat-cell *matCellDef="let instruction">{{instruction.instructionType.value}}/{{instruction.amount}}</td>
      </ng-container>

      <ng-container matColumnDef="validity">
        <th mat-header-cell *matHeaderCellDef> Validity </th>
        <td mat-cell *matCellDef="let instruction">{{instruction.validFrom  | dateFormat}} to {{instruction.validTill  | dateFormat}}</td>
      </ng-container>

      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef> Actions </th>
        <td mat-cell *matCellDef="let instruction">
          <span *ngIf="instruction.status.value!=='Deleted'">
            <button class="account-action-button" mat-raised-button color="primary" matTooltip="Edit Standing Instruction"
              *mifosxHasPermission="'UPDATE_STANDINGINSTRUCTION'">
              <i class="fa fa-edit"></i>
            </button>
          </span>
          <span *ngIf="instruction.status.value!=='Deleted'">
            <button class="account-action-button" mat-raised-button color="warn" matTooltip="Delete Standing Instruction"
              *mifosxHasPermission="'DELETE_STANDINGINSTRUCTION'" (click)="deleteStandingInstruction(instruction.id)">
              <i class="fa fa-times"></i>
            </button>
          </span>
          <button class="account-action-button" mat-raised-button color="primary" matTooltip="View Standing Instruction"
            *mifosxHasPermission="'READ_STANDINGINSTRUCTION'">
            <i class="fa fa-eye"></i>
          </button>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

    </table>

  </div>

</div>
