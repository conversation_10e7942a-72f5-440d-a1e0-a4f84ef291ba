<div class="container">

  <mat-card>

    <form [formGroup]="editTransactionForm" (ngSubmit)="submit()">

      <mat-card-content fxLayout="column">

        <mat-form-field (click)="dueDatePicker.open()">
          <mat-label>Transaction Date</mat-label>
          <input matInput [min]="minDate" [max]="maxDate" [matDatepicker]="dueDatePicker"
            formControlName="transactionDate" required>
          <mat-datepicker-toggle matSuffix [for]="dueDatePicker"></mat-datepicker-toggle>
          <mat-datepicker #dueDatePicker></mat-datepicker>
          <mat-error *ngIf="editTransactionForm.controls.transactionDate.hasError('required')">
            Transaction Date is <strong>required</strong>
          </mat-error>
        </mat-form-field>

        <mat-form-field>
          <mat-label>Transaction Amount</mat-label>
          <input type="number" formControlName="transactionAmount" required matInput />
          <mat-error *ngIf="editTransactionForm.controls.transactionAmount.hasError('required')">
            Transaction Amount is <strong>required</strong>
          </mat-error>
        </mat-form-field>

        <mat-form-field>
          <mat-label>External Id</mat-label>
          <input matInput formControlName="externalId">
        </mat-form-field>

        <mat-form-field>
          <mat-label>Payment Type</mat-label>
          <mat-select formControlName="paymentTypeId">
            <mat-option *ngFor="let paymentType of paymentTypeOptions" [value]="paymentType.id">
              {{ paymentType.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <div fxFlexFill>
          <span fxFlex="75%" class="expandcollapsebutton m-l-10 m-t-40" (click)="addPaymentDetails()">
            <mat-slide-toggle>
              <div [className]="showPaymentDetails ? 'enabled' : 'disabled'">
                <span class="m-l-10">Show Payment Details</span>
              </div>
            </mat-slide-toggle>
          </span>
        </div>

        <mat-form-field *ngIf="editTransactionForm.controls['accountNumber']">
          <mat-label>Account Number</mat-label>
          <input type="number" formControlName="accountNumber" matInput />
        </mat-form-field>

        <mat-form-field *ngIf="editTransactionForm.controls['checkNumber']">
          <mat-label>Cheque</mat-label>
          <input type="number" formControlName="checkNumber" matInput />
        </mat-form-field>

        <mat-form-field *ngIf="editTransactionForm.controls['routingCode']">
          <mat-label>Routing Code</mat-label>
          <input formControlName="routingCode" matInput />
        </mat-form-field>

        <mat-form-field *ngIf="editTransactionForm.controls['receiptNumber']">
          <mat-label>Receipt Number</mat-label>
          <input formControlName="receiptNumber" matInput />
        </mat-form-field>

        <mat-form-field *ngIf="editTransactionForm.controls['bankNumber']">
          <mat-label>Bank</mat-label>
          <input formControlName="bankNumber" matInput />
        </mat-form-field>

        <mat-card-actions fxLayoutGap="5px" fxLayout="row" fxLayout.xs="column" fxLayoutAlign="center">
          <button type="button" mat-raised-button [routerLink]="['../']">Cancel</button>
          <button mat-raised-button color="primary" [disabled]="!editTransactionForm.valid">Submit</button>
        </mat-card-actions>

      </mat-card-content>

    </form>

  </mat-card>

</div>
