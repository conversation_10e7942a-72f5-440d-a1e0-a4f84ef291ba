<div class="container mat-elevation-z8">

  <mat-card>

    <form [formGroup]="foreclosureForm" (ngSubmit)="submit()">

      <mat-card-content>

        <div fxLayout="column">

          <mat-form-field (click)="transactionsDatePicker.open()">
            <mat-label>Transaction Date</mat-label>
            <input matInput [min]="minDate" [max]="maxDate" [matDatepicker]="transactionsDatePicker" required
              formControlName="transactionDate">
            <mat-datepicker-toggle matSuffix [for]="transactionsDatePicker"></mat-datepicker-toggle>
            <mat-datepicker #transactionsDatePicker></mat-datepicker>
            <mat-error *ngIf="foreclosureForm.controls.transactionDate.hasError('required')">
              Transaction Date <strong>is required</strong>
            </mat-error>
          </mat-form-field>

          <mat-form-field>
            <mat-label>Principal</mat-label>
            <input matInput formControlName="outstandingPrincipalPortion">
          </mat-form-field>

          <mat-form-field>
            <mat-label>Interest</mat-label>
            <input matInput formControlName="outstandingInterestPortion">
          </mat-form-field>

          <mat-form-field>
            <mat-label>Fee Amount</mat-label>
            <input matInput formControlName="outstandingFeeChargesPortion">
          </mat-form-field>

          <mat-form-field>
            <mat-label>Penalty Amount</mat-label>
            <input matInput required formControlName="outstandingPenaltyChargesPortion">
          </mat-form-field>

          <mat-form-field>
            <mat-label>Transaction Amount</mat-label>
            <input matInput required formControlName="transactionAmount">
          </mat-form-field>


          <mat-form-field>
            <mat-label>Payment Type</mat-label>
            <mat-select formControlName="paymentTypeId">
              <mat-option *ngFor="let paymentType of paymentTypes" [value]="paymentType.id">
                {{ paymentType.name }}
              </mat-option>
            </mat-select>
            <mat-error *ngIf="foreclosureForm.controls.paymentTypeId.hasError('required')">
              Payment Type <strong>is required</strong>
            </mat-error>
          </mat-form-field>

          <div fxFlexFill>
            <span fxFlex="75%" class="expandcollapsebutton m-l-10 m-t-40" (click)="addPaymentDetails()">
              <mat-slide-toggle>
                <div [className]="showPaymentDetails ? 'enabled' : 'disabled'">
                  <span class="m-l-10">Show Payment Details</span>
                </div>
              </mat-slide-toggle>
            </span>
          </div>

          <ng-container *ngIf="showPaymentDetails">
            <mat-form-field>
              <mat-label> Account #</mat-label>
              <input matInput formControlName="accountNumber">
            </mat-form-field>

            <mat-form-field>
              <mat-label>Cheque #</mat-label>
              <input matInput formControlName="checkNumber">
            </mat-form-field>

            <mat-form-field>
              <mat-label>Routing Code</mat-label>
              <input matInput formControlName="routingCode">
            </mat-form-field>

            <mat-form-field>
              <mat-label>Reciept #</mat-label>
              <input matInput formControlName="receiptNumber">
            </mat-form-field>

            <mat-form-field>
              <mat-label>Bank #</mat-label>
              <input matInput formControlName="bankNumber">
            </mat-form-field>
          </ng-container>

          <mat-form-field>
            <mat-label>Note</mat-label>
            <textarea matInput formControlName="note" cdkTextareaAutosize cdkAutosizeMinRows="2"></textarea>
          </mat-form-field>

        </div>

        <mat-card-actions fxLayout="row" fxLayout.xs="column" fxLayoutAlign="center" fxLayoutGap="5px">
          <button type="button" mat-raised-button [routerLink]="['../../general']">Cancel</button>
          <button mat-raised-button color="primary" [disabled]="!(foreclosureForm.valid && completed)"
            *mifosxHasPermission="'FORECLOSURE_LOAN'">Foreclosure</button>
        </mat-card-actions>

      </mat-card-content>

    </form>

  </mat-card>

</div>
