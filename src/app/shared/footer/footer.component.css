/*
Error: File to import not found or unreadable: assets/styles/helper.
        on line 1 of footer.component.scss

1: @import "assets/styles/helper";
2: 
3: #footer {
4:   align-items: center;
5:   //position: relative;
6:   //vertical-align: bottom;

Backtrace:
footer.component.scss:1
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/import_node.rb:67:in `rescue in import'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/import_node.rb:44:in `import'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/import_node.rb:28:in `imported_file'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/import_node.rb:37:in `css_import?'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/visitors/perform.rb:310:in `visit_import'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/visitors/base.rb:36:in `visit'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/visitors/perform.rb:158:in `block in visit'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/stack.rb:79:in `block in with_base'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/stack.rb:135:in `with_frame'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/stack.rb:79:in `with_base'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/visitors/perform.rb:158:in `visit'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/visitors/base.rb:52:in `block in visit_children'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/visitors/base.rb:52:in `map'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/visitors/base.rb:52:in `visit_children'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/visitors/perform.rb:167:in `block in visit_children'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/visitors/perform.rb:179:in `with_environment'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/visitors/perform.rb:166:in `visit_children'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/visitors/base.rb:36:in `block in visit'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/visitors/perform.rb:186:in `visit_root'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/visitors/base.rb:36:in `visit'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/visitors/perform.rb:157:in `visit'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/visitors/perform.rb:10:in `visit'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/root_node.rb:36:in `css_tree'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/tree/root_node.rb:29:in `render_with_sourcemap'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/engine.rb:389:in `_render_with_sourcemap'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/engine.rb:307:in `render_with_sourcemap'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/exec/sass_scss.rb:387:in `run'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/exec/sass_scss.rb:63:in `process_result'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/exec/base.rb:50:in `parse'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/lib/sass/exec/base.rb:18:in `parse!'
C:/Users/<USER>/.local/share/gem/ruby/3.2.0/gems/sass-3.7.4/bin/sass:13:in `<top (required)>'
C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/sass:32:in `load'
C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/sass:32:in `<main>'
*/
body:before {
  white-space: pre;
  font-family: monospace;
  content: "Error: File to import not found or unreadable: assets/styles/helper.\A         on line 1 of footer.component.scss\A \A 1: @import \"assets/styles/helper\";\A 2: \A 3: #footer {\A 4:   align-items: center;\A 5:   //position: relative;\A 6:   //vertical-align: bottom;"; }
