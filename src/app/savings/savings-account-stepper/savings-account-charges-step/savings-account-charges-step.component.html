<div fxLayout="row wrap" fxLayoutGap="2%" fxLayout.lt-md="column">

  <mat-form-field fxFlex="48%">
    <mat-label>Charge</mat-label>
    <mat-select #charge disabled>
      <mat-option *ngFor="let charge of chargeData | chargesFilter:chargesDataSource:currencyCode.value"
        [value]="charge">
        {{ charge.name }}
      </mat-option>
    </mat-select>
  </mat-form-field>

  <div fxFlex="48%" fxFlexAlign="center">
    <button type="button" mat-raised-button color="primary" (click)="addCharge(charge)" [disabled]="!charge.value">
      <fa-icon icon="plus" class="m-r-10"></fa-icon>
      Add
    </button>
  </div>

  <table fxFlex="98%" class="mat-elevation-z1" mat-table [dataSource]="chargesDataSource"
    *ngIf="chargesDataSource.length > 0">

    <ng-container matColumnDef="name">
      <th mat-header-cell *matHeaderCellDef> Name </th>
      <td mat-cell *matCellDef="let charge">
        {{ charge.name + ', ' + charge.currency.displaySymbol }}
      </td>
    </ng-container>

    <ng-container matColumnDef="chargeCalculationType">
      <th mat-header-cell *matHeaderCellDef> Type </th>
      <td mat-cell *matCellDef="let charge">
        {{ charge.chargeCalculationType.value }}
      </td>
    </ng-container>

    <ng-container matColumnDef="amount">
      <th mat-header-cell *matHeaderCellDef> Amount </th>
      <td mat-cell *matCellDef="let charge">
        {{ charge.amount }}
        <button mat-icon-button color="primary" (click)="editChargeAmount(charge)" disabled>
          <fa-icon icon="pen"></fa-icon>
        </button>
      </td>
    </ng-container>

    <ng-container matColumnDef="chargeTimeType">
      <th mat-header-cell *matHeaderCellDef> Collected On </th>
      <td mat-cell *matCellDef="let charge">
        {{ charge.chargeTimeType.value }}
      </td>
    </ng-container>

    <ng-container matColumnDef="date">
      <th mat-header-cell *matHeaderCellDef> Date </th>
      <td mat-cell *matCellDef="let charge">
        <span
          *ngIf="charge.chargeTimeType.value === 'Specified due date' || charge.chargeTimeType.value === 'Weekly Fee'">
          {{(charge.dueDate | dateFormat) || 'Unassigned'}}
        </span>
        <span *ngIf="charge.chargeTimeType.value === 'Monthly Fee' || charge.chargeTimeType.value === 'Annual Fee'">
          {{(charge.feeOnMonthDay | dateFormat) || 'Unassigned'}}
        </span>
        <span *ngIf="!(charge.chargeTimeType.value === 'Monthly Fee' || charge.chargeTimeType.value === 'Annual Fee'
            || charge.chargeTimeType.value === 'Specified due date' || charge.chargeTimeType.value === 'Weekly Fee')">
          N/A
        </span>
        <button mat-icon-button color="primary" *ngIf="charge.chargeTimeType.value === 'Weekly Fee' || charge.chargeTimeType.value === 'Annual Fee'
                  || charge.chargeTimeType.value === 'Specified due date'" (click)="editChargeDate(charge)">
          <fa-icon icon="pen"></fa-icon>
        </button>
      </td>
    </ng-container>

    <ng-container matColumnDef="repaymentsEvery">
      <th mat-header-cell *matHeaderCellDef> Repayments Every </th>
      <td mat-cell *matCellDef="let charge">
        {{ charge.feeInterval || 'Not Provided' }}
        <button mat-icon-button color="primary"
          *ngIf="charge.chargeTimeType.value === 'Weekly Fee' || charge.chargeTimeType.value === 'Monthly Fee'"
          (click)="editChargeFeeInterval(charge)" disabled>
          <fa-icon icon="pen"></fa-icon>
        </button>
      </td>
    </ng-container>

    <ng-container matColumnDef="action">
      <th mat-header-cell *matHeaderCellDef> Actions </th>
      <td mat-cell *matCellDef="let charge">
        <button mat-icon-button color="warn" (click)="deleteCharge(charge)" disabled>
          <fa-icon icon="trash"></fa-icon>
        </button>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

  </table>

  <div fxFlex="98%" *ngIf="activeClientMembers">
    <table fxFlex="98%" mat-table [dataSource]="dataSource">

      <!-- Check Column -->
      <ng-container matColumnDef="check">
          <th mat-header-cell *matHeaderCellDef>
              <mat-checkbox (change)="toggleSelects()" [(ngModel)]="selectAllItems"></mat-checkbox>
          </th>
          <td mat-cell *matCellDef="let element">
              <mat-checkbox (change)="toggleSelect()" [(ngModel)]="element.selected"></mat-checkbox>
          </td>
      </ng-container>

      <!-- Position Column -->
      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef> Client ID </th>
        <td mat-cell *matCellDef="let element"> {{element.id}} </td>
      </ng-container>

      <!-- Name Column -->
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef> Client Name </th>
        <td mat-cell *matCellDef="let element"> {{element.displayName}} </td>
      </ng-container>


      <tr mat-header-row *matHeaderRowDef="displayedColumn"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumn;"></tr>
    </table>
  </div>


</div>

<div fxLayout="row" class="margin-t" fxLayout.xs="column" fxLayoutAlign="center" fxLayoutGap="2%">
  <button mat-raised-button matStepperPrevious>
    <fa-icon icon="arrow-left" class="m-r-10"></fa-icon>
    Previous
  </button>
  <button mat-raised-button matStepperNext>
    Next
    <fa-icon icon="arrow-right" class="m-l-10"></fa-icon>
  </button>
</div>
