<div class="tab-container mat-typography">

  <div class="mat-elevation-z1 m-b-25" [hidden]="!accountWithTransactions">

    <div fxLayout="row" fxLayoutAlign="start">
      <div class="m-b-10">
        <h3>All Transactions</h3>
      </div>
      <div class="action-button m-b-20" fxLayout="row" fxLayoutGap="20px" *ngIf="checkStatus()">
        <button mat-raised-button color="primary" [routerLink]="['/accounting', 'journal-entries']">View Journal Entries</button>
        <button mat-raised-button color="primary" [routerLink]="['export']">Export</button>
      </div>
    </div>

    <table mat-table [dataSource]="dataSource" matSort>

      <ng-container matColumnDef="id">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Id </th>
        <td mat-cell *matCellDef="let transaction"> {{ transaction.id }} </td>
      </ng-container>

      <ng-container matColumnDef="date">
        <th mat-header-cell class="center" *matHeaderCellDef mat-sort-header> Transaction Date </th>
        <td mat-cell *matCellDef="let transaction"> {{ transaction.date  | dateFormat }} </td>
      </ng-container>

      <ng-container matColumnDef="transactionType">
        <th mat-header-cell class="center" *matHeaderCellDef> Transaction Type </th>
        <td mat-cell *matCellDef="let transaction"> {{ transaction.transactionType.value  }} </td>
      </ng-container>

      <ng-container matColumnDef="debit">
        <th mat-header-cell class="r-amount" *matHeaderCellDef> Debit </th>
        <td mat-cell class="r-amount" *matCellDef="let transaction"> {{ isDebit(transaction.transactionType) ? (transaction.amount | formatNumber) : 'N/A'}} </td>
      </ng-container>

      <ng-container matColumnDef="credit">
        <th mat-header-cell class="r-amount" *matHeaderCellDef> Credit </th>
        <td mat-cell class="r-amount" *matCellDef="let transaction"> {{ !isDebit(transaction.transactionType) ? (transaction.amount | formatNumber) : 'N/A' }} </td>
      </ng-container>

      <ng-container matColumnDef="balance">
        <th mat-header-cell class="r-amount" *matHeaderCellDef> Balance </th>
        <td mat-cell class="r-amount" *matCellDef="let transaction"> {{ transaction.runningBalance | formatNumber }} </td>
      </ng-container>

      <ng-container matColumnDef="viewReciept">
        <th mat-header-cell class="center" *matHeaderCellDef> View Reciept </th>
        <td mat-cell class="center" *matCellDef="let transaction">
          <button class="account-action-button" mat-raised-button color="primary" (click)="routeEdit($event)" [routerLink]="[transaction.id, 'reciept']">
            <i class="fa fa-file" matTooltip="View Reciept"></i>
          </button>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;" class="select-row" (click)="showTransactions(row)"></tr>

    </table>

    <mat-paginator [pageSize]="50" [pageSizeOptions]="[50, 100, 200]" showFirstLastButtons></mat-paginator>

  </div>

  <div class="alert" [hidden]="accountWithTransactions">
    <div class="message">
      <i class="fa fa-exclamation-circle alert-check"></i>
      No transaction was found
    </div>
  </div>
</div>
