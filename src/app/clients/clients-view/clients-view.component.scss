.client-card {
  margin: 0 auto;
  max-width: 80rem;
  width: 90%;
  padding: 0;
  .header {
    padding: 1%;
    .header-title-group {
      .client-card-title {
        color: white;
        width: 90%;
        p {
          color: white;
        }
      }
    }
    .profile-image-container {
      margin: 4px;
      .profile-image {
        object-fit: cover;
        border-radius: 20px;
      }
      .client-image-button {
        min-width: 26px;
        padding: 0 6px;
        margin: 4px;
        line-height: 25px;
      }
      .signature {
        background-color: #065F46;
        color: white;
        text-align: center;
        margin: 0;
        padding: 4%;
        &:hover {
          cursor: pointer;
        }
      }
    }
    .client-actions {
      align-self: flex-end;
      margin: 0 1%;
      i {
        margin-bottom: 2px;
        margin-right: 4px;
      }
    }
  }
  .navigation-tabs {
    overflow: auto;
  }
  i:hover {
    cursor: pointer;
  }
}
