<form [formGroup]="recurringDepositProductInterestRateChartForm">

  <div fxLayout="row wrap" fxLayoutGap="2%" fxLayout.lt-md="column">

    <h3 fxFlex="13%" class="mat-h3">Interest Rate Charts</h3>

    <div fxFlex="83%" fxLayout="row" fxLayoutAlign="start center">
      <button type="button" mat-raised-button color="primary" (click)="addChart()">
        <fa-icon icon="plus" class="m-r-10"></fa-icon>
        Add
      </button>
    </div>

    <div fxFlexFill fxLayout="row wrap" fxLayout.lt-md="column" formArrayName="charts" *ngFor="let chart of charts.controls; let chartIndex = index">

      <mat-divider fxFlex="98%"></mat-divider>

      <div fxFlexFill fxLayout="row wrap" fxLayoutGap="2%" fxLayout.lt-md="column" [formGroupName]="chartIndex">

        <div fxFlex="98%" fxLayout="row" fxLayoutAlign="end center">
          <button type="button" mat-icon-button color="warn" (click)="delete(charts, chartIndex)" matTooltip="Delete Interest Rate Chart" matTooltipPosition="left">
            <fa-icon icon="trash"></fa-icon>
          </button>
        </div>

        <mat-form-field fxFlex="32%">
          <mat-label>Name</mat-label>
          <input matInput formControlName="name" required>
        </mat-form-field>

        <mat-form-field fxFlex="31%" (click)="validFromDatePicker.open()">
          <mat-label>Valid from Date</mat-label>
          <input matInput [min]="minDate" [max]="maxDate" [matDatepicker]="validFromDatePicker" formControlName="fromDate" required>
          <mat-datepicker-toggle matSuffix [for]="validFromDatePicker"></mat-datepicker-toggle>
          <mat-datepicker #validFromDatePicker></mat-datepicker>
          <mat-error>
            Valid from Date is <strong>required</strong>
          </mat-error>
        </mat-form-field>

        <mat-form-field fxFlex="31%" (click)="endDatePicker.open()">
          <mat-label>End Date</mat-label>
          <input matInput [min]="minDate" [max]="maxDate" [matDatepicker]="endDatePicker" formControlName="endDate">
          <mat-datepicker-toggle matSuffix [for]="endDatePicker"></mat-datepicker-toggle>
          <mat-datepicker #endDatePicker></mat-datepicker>
        </mat-form-field>

        <mat-form-field fxFlex="65%">
          <mat-label>Description</mat-label>
          <textarea matInput formControlName="description" required></textarea>
        </mat-form-field>

        <div fxFlex="31%" fxLayout="row" fxLayoutAlign="space-between center">
          <mat-checkbox labelPosition="before" formControlName="isPrimaryGroupingByAmount">
            Is primary grouping by amount?
          </mat-checkbox>
          <button type="button" mat-raised-button color="primary" (click)="addChartSlab(chart.controls.chartSlabs)">
            <fa-icon icon="plus" class="m-r-10"></fa-icon>
            Add Slab
          </button>
        </div>

        <div fxFlex="100%" *ngIf="chart.value.chartSlabs.length === 0">
          <h3 class="mat-h3">It's required to add at least one Slab</h3>
        </div>

        <table fxFlex="98%" class="mat-elevation-z1" mat-table [dataSource]="chart.value.chartSlabs" *ngIf="chart.value.chartSlabs.length !== 0" multiTemplateDataRows>

          <ng-container matColumnDef="period">
            <th mat-header-cell *matHeaderCellDef> Period </th>
            <td mat-cell *matCellDef="let chartSlab">
              {{ chartSlab.fromPeriod + ' - ' + chartSlab.toPeriod + ' ' + (chartSlab.periodType | find:periodTypeData:'id':'value') }}
            </td>
          </ng-container>

          <ng-container matColumnDef="amountRange">
            <th mat-header-cell *matHeaderCellDef> Amount Range </th>
            <td mat-cell *matCellDef="let chartSlab">
              {{ chartSlab.amountRangeFrom + ' - ' + chartSlab.amountRangeTo }}
            </td>
          </ng-container>

          <ng-container matColumnDef="annualInterestRate">
            <th mat-header-cell *matHeaderCellDef> Interest </th>
            <td mat-cell *matCellDef="let chartSlab">
              {{ chartSlab.annualInterestRate }}
            </td>
          </ng-container>

          <ng-container matColumnDef="description">
            <th mat-header-cell *matHeaderCellDef> Description </th>
            <td mat-cell *matCellDef="let chartSlab">
              {{ chartSlab.description }}
            </td>
          </ng-container>

          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> Actions </th>
            <td mat-cell *matCellDef="let chartSlab; let chartSlabIndex = dataIndex">
              <button mat-icon-button color="primary" (click)="editChartSlab(chart.controls.chartSlabs, chartSlabIndex)">
                <fa-icon icon="edit"></fa-icon>
              </button>
              <button mat-icon-button color="warn" (click)="delete(chart.controls.chartSlabs, chartSlabIndex)">
                <fa-icon icon="trash"></fa-icon>
              </button>
              <button mat-button color="primary" (click)="expandChartSlabIndex[chartIndex] = expandChartSlabIndex[chartIndex] === chartSlabIndex ? null : chartSlabIndex">
                <span *ngIf="expandChartSlabIndex[chartIndex] !== chartSlabIndex">
                  <fa-icon icon="eye" class="m-r-10"></fa-icon>
                  View Incentives
                </span>
                <span *ngIf="expandChartSlabIndex[chartIndex] === chartSlabIndex">
                  <fa-icon icon="eye-slash" class="m-r-10"></fa-icon>
                  Hide Incentives
                </span>
              </button>
            </td>
          </ng-container>

          <ng-container matColumnDef="incentives">
            <td mat-cell *matCellDef="let chartSlab; let chartSlabIndex = dataIndex" [attr.colspan]="chartSlabsDisplayedColumns[chartIndex].length">

              <div fxLayout="row wrap" fxFlexFill class="incentives" [@expandChartSlab]="chartSlabIndex === expandChartSlabIndex[chartIndex] ? 'expanded' : 'collapsed'">

                <mat-card fxLayout="row wrap" fxFlexFill>

                  <h4 class="mat-h4" fxFlex="13%">
                    Incentives
                  </h4>

                  <div fxFlex="83%">
                    <button mat-raised-button color="primary" (click)="addIncentive(getIncentives(chart.controls.chartSlabs, chartSlabIndex))">
                      <fa-icon icon="plus" class="m-r-10"></fa-icon>
                      Add
                    </button>
                  </div>

                  <table fxFlexFill class="mat-elevation-z1" mat-table [dataSource]="chartSlab.incentives" *ngIf="chartSlab.incentives.length">

                    <ng-container matColumnDef="entityType">
                      <th mat-header-cell *matHeaderCellDef> Entity Type </th>
                      <td mat-cell *matCellDef="let incentive">
                        {{ incentive.entityType | find:entityTypeData:'id':'value' }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="attributeName">
                      <th mat-header-cell *matHeaderCellDef> Attribute Name </th>
                      <td mat-cell *matCellDef="let incentive">
                        {{ incentive.attributeName | find:attributeNameData:'id':'value' }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="conditionType">
                      <th mat-header-cell *matHeaderCellDef> Condition Type </th>
                      <td mat-cell *matCellDef="let incentive">
                        {{ incentive.conditionType | find:conditionTypeData:'id':'value' }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="attributeValue">
                      <th mat-header-cell *matHeaderCellDef> Attribute Value </th>
                      <td mat-cell *matCellDef="let incentive" [ngSwitch]="incentive.attributeName">
                        <span *ngSwitchCase="2">{{ incentive.attributeValue | find:genderData:'id':'name' }}</span>
                        <span *ngSwitchCase="3">{{ incentive.attributeValue }}</span>
                        <span *ngSwitchCase="4">{{ incentive.attributeValue | find:clientTypeData:'id':'name' }}</span>
                        <span *ngSwitchCase="5">{{ incentive.attributeValue | find:clientClassificationData:'id':'name' }}</span>
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="incentiveType">
                      <th mat-header-cell *matHeaderCellDef> Incentive Type </th>
                      <td mat-cell *matCellDef="let incentive">
                        {{ incentive.incentiveType | find:incentiveTypeData:'id':'value'}}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="amount">
                      <th mat-header-cell *matHeaderCellDef> Interest </th>
                      <td mat-cell *matCellDef="let incentive">
                        {{ incentive.amount }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="actions">
                      <th mat-header-cell *matHeaderCellDef> Actions </th>
                      <td mat-cell *matCellDef="let incentive; let incentiveIndex = index">
                        <button mat-icon-button color="primary" (click)="editIncentive(getIncentives(chart.controls.chartSlabs, chartSlabIndex), incentiveIndex)">
                          <fa-icon icon="edit"></fa-icon>
                        </button>
                        <button mat-icon-button color="warn" (click)="delete(getIncentives(chart.controls.chartSlabs, chartSlabIndex), incentiveIndex)">
                          <fa-icon icon="trash"></fa-icon>
                        </button>
                      </td>
                    </ng-container>

                    <tr mat-header-row *matHeaderRowDef="incentivesDisplayedColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: incentivesDisplayedColumns;"></tr>

                  </table>

                </mat-card>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="chartSlabsDisplayedColumns[chartIndex]"></tr>
          <tr mat-row *matRowDef="let row; columns: chartSlabsDisplayedColumns[chartIndex];"></tr>
          <tr mat-row *matRowDef="let row; columns: chartSlabsIncentivesDisplayedColumns;" class="incentives-row"></tr>

        </table>

      </div>

    </div>

  </div>

  <div fxLayout="row" class="margin-t" fxLayout.xs="column" fxLayoutAlign="center" fxLayoutGap="2%">
    <button mat-raised-button matStepperPrevious disabled>
      <fa-icon icon="arrow-left" class="m-r-10"></fa-icon>
      Previous
    </button>
    <button mat-raised-button matStepperNext>
      Next
      <fa-icon icon="arrow-right" class="m-l-10"></fa-icon>
    </button>
  </div>

</form>
