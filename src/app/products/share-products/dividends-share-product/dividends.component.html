<div class="container m-b-20" fxLayout="row" fxLayoutAlign="end" fxLayoutGap="20px">
    <button mat-raised-button color="primary" [routerLink]="['create']" *mifosxHasPermission="'CREATE_SHAREDIVIDEND'">
      <fa-icon icon="plus" class="m-r-10"></fa-icon>
      Initiate Dividend
    </button>
</div>

<div class="container">

  <div fxLayout="row">
    <mat-form-field fxFlex>
      <mat-label>{{ 'labels.inputs.Filter' | translate }}</mat-label>
      <input matInput (keyup)="applyFilter($event.target.value)">
    </mat-form-field>
  </div>

  <div class="mat-elevation-z8">

    <table mat-table [dataSource]="dataSource" matSort>

      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Name </th>
        <td mat-cell *matCellDef="let dividend">{{ dividend.productData.name }}</td>
      </ng-container>

      <ng-container matColumnDef="dividendPeriodStartDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Dividend Period Start Date </th>
        <td mat-cell *matCellDef="let dividend">{{ dividend.dividendPeriodStartDate  | dateFormat }}</td>
      </ng-container>

      <ng-container matColumnDef="dividendPeriodEndDate">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Dividend Period End Date </th>
        <td mat-cell *matCellDef="let dividend">{{ dividend.dividendPeriodEndDate  | dateFormat }}</td>
      </ng-container>

      <ng-container matColumnDef="amount">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Amount </th>
        <td mat-cell *matCellDef="let dividend"> {{ dividend.amount }}</td>
      </ng-container>

      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef mat-sort-header> Status </th>
        <td mat-cell *matCellDef="let dividend">{{ dividend.status.value }}</td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;" (click)="showDividend(row.id, row.status.value)" class="select-row"></tr>

    </table>

    <mat-paginator [pageSizeOptions]="[10, 25, 50, 100]" showFirstLastButtons></mat-paginator>

  </div>

</div>
