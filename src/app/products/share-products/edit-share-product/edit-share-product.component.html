<div class="container">

  <mat-horizontal-stepper class="mat-elevation-z8" labelPosition="bottom" #shareProductStepper>

    <ng-template matStepperIcon="number">
      <fa-icon icon="pencil-alt" size="sm"></fa-icon>
    </ng-template>

    <ng-template matStepperIcon="edit">
      <fa-icon icon="pencil-alt" size="sm"></fa-icon>
    </ng-template>

    <ng-template matStepperIcon="done">
      <fa-icon icon="check" size="sm"></fa-icon>
    </ng-template>

    <ng-template matStepperIcon="error">
      <fa-icon icon="exclamation-triangle" size="lg"></fa-icon>
    </ng-template>

    <ng-template matStepperIcon="preview">
      <fa-icon icon="eye" size="sm"></fa-icon>
    </ng-template>

    <mat-step [stepControl]="shareProductDetailsForm" completed>

      <ng-template matStepLabel>DETAILS</ng-template>

      <mifosx-share-product-details-step [shareProductsTemplate]="shareProductAndTemplate"></mifosx-share-product-details-step>

    </mat-step>

    <mat-step [stepControl]="shareProductCurrencyForm" completed>

      <ng-template matStepLabel>CURRENCY</ng-template>

      <mifosx-share-product-currency-step [shareProductsTemplate]="shareProductAndTemplate"></mifosx-share-product-currency-step>

    </mat-step>

    <mat-step [stepControl]="shareProductTermsForm" completed>

      <ng-template matStepLabel>TERMS</ng-template>

      <mifosx-share-product-terms-step [shareProductsTemplate]="shareProductAndTemplate"></mifosx-share-product-terms-step>

    </mat-step>

    <mat-step [stepControl]="shareProductSettingsForm" completed>

      <ng-template matStepLabel>SETTINGS</ng-template>

      <mifosx-share-product-settings-step [shareProductsTemplate]="shareProductAndTemplate"></mifosx-share-product-settings-step>

    </mat-step>

    <mat-step [stepControl]="shareProductMarketPriceForm" completed>

      <ng-template matStepLabel>MARKET PRICE</ng-template>

      <mifosx-share-product-market-price-step [shareProductsTemplate]="shareProductAndTemplate"></mifosx-share-product-market-price-step>

    </mat-step>

    <mat-step completed>

      <ng-template matStepLabel>CHARGES</ng-template>

      <mifosx-share-product-charges-step
        [shareProductsTemplate]="shareProductAndTemplate"
        [currencyCode]="shareProductCurrencyForm.get('currencyCode')"
      >
      </mifosx-share-product-charges-step>

    </mat-step>

    <mat-step [stepControl]="shareProductAccountingForm" completed>

      <ng-template matStepLabel>ACCOUNTING</ng-template>

      <mifosx-share-product-accounting-step
        [shareProductsTemplate]="shareProductAndTemplate"
        [accountingRuleData]="accountingRuleData"
        [shareProductFormValid]="shareProductFormValidAndNotPristine"
      >
      </mifosx-share-product-accounting-step>

    </mat-step>

    <mat-step state="preview" *ngIf="shareProductFormValidAndNotPristine" completed>

      <ng-template matStepLabel>PREVIEW</ng-template>

      <mifosx-share-product-preview-step
        [shareProductsTemplate]="shareProductAndTemplate"
        [accountingRuleData]="accountingRuleData"
        [shareProduct]="shareProduct"
        [taskPermission]='"UPDATE_SHAREPRODUCT"'
        (submit)="submit()"
      >
      </mifosx-share-product-preview-step>

    </mat-step>

  </mat-horizontal-stepper>

</div>
