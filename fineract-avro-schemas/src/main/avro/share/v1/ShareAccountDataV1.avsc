{"name": "ShareAccountDataV1", "namespace": "org.apache.fineract.avro.share.v1", "type": "record", "fields": [{"default": null, "name": "id", "type": ["null", "long"]}, {"default": null, "name": "accountNo", "type": ["null", "string"]}, {"default": null, "name": "externalId", "type": ["null", "string"]}, {"default": null, "name": "savingsAccountNumber", "type": ["null", "string"]}, {"default": null, "name": "clientId", "type": ["null", "long"]}, {"default": null, "name": "clientName", "type": ["null", "string"]}, {"default": null, "name": "defaultShares", "type": ["null", "long"]}, {"default": null, "name": "productId", "type": ["null", "long"]}, {"default": null, "name": "productName", "type": ["null", "string"]}, {"default": null, "name": "status", "type": ["null", "org.apache.fineract.avro.share.v1.ShareAccountStatusEnumDataV1"]}, {"default": null, "name": "timeline", "type": ["null", "org.apache.fineract.avro.share.v1.ShareAccountApplicationTimelineDataV1"]}, {"default": null, "name": "currency", "type": ["null", "org.apache.fineract.avro.generic.v1.CurrencyDataV1"]}, {"default": null, "name": "summary", "type": ["null", "org.apache.fineract.avro.share.v1.ShareAccountSummaryDataV1"]}, {"default": null, "name": "purchasedShares", "type": ["null", {"type": "array", "items": "org.apache.fineract.avro.share.v1.ShareAccountTransactionDataV1"}]}, {"default": null, "name": "savingsAccountId", "type": ["null", "long"]}, {"default": null, "name": "currentMarketPrice", "type": ["null", "bigdecimal"]}, {"default": null, "name": "lockinPeriod", "type": ["null", "int"]}, {"default": null, "name": "lockPeriodTypeEnum", "type": ["null", "org.apache.fineract.avro.generic.v1.EnumOptionDataV1"]}, {"default": null, "name": "minimumActivePeriod", "type": ["null", "int"]}, {"default": null, "name": "minimumActivePeriodTypeEnum", "type": ["null", "org.apache.fineract.avro.generic.v1.EnumOptionDataV1"]}, {"default": null, "name": "allowDividendCalculationForInactiveClients", "type": ["null", "boolean"]}]}