{"name": "PortfolioAccountDataV1", "namespace": "org.apache.fineract.avro.portfolio.v1", "type": "record", "fields": [{"default": null, "name": "id", "type": ["null", "long"]}, {"default": null, "name": "accountNo", "type": ["null", "string"]}, {"default": null, "name": "externalId", "type": ["null", "string"]}, {"default": null, "name": "groupId", "type": ["null", "long"]}, {"default": null, "name": "groupName", "type": ["null", "string"]}, {"default": null, "name": "clientId", "type": ["null", "long"]}, {"default": null, "name": "clientName", "type": ["null", "string"]}, {"default": null, "name": "productId", "type": ["null", "long"]}, {"default": null, "name": "productName", "type": ["null", "string"]}, {"default": null, "name": "fieldOfficerId", "type": ["null", "long"]}, {"default": null, "name": "fieldOfficerName", "type": ["null", "string"]}, {"default": null, "name": "currency", "type": ["null", "org.apache.fineract.avro.generic.v1.CurrencyDataV1"]}, {"default": null, "name": "amtForTransfer", "type": ["null", "bigdecimal"]}]}