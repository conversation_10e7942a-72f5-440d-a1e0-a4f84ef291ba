{"name": "PaymentTypeDataV1", "namespace": "org.apache.fineract.avro.payment.v1", "type": "record", "fields": [{"default": null, "name": "id", "type": ["null", "long"]}, {"default": null, "name": "name", "type": ["null", "string"]}, {"default": null, "name": "description", "type": ["null", "string"]}, {"default": null, "name": "isCashPayment", "type": ["null", "boolean"]}, {"default": null, "name": "position", "type": ["null", "long"]}, {"default": null, "name": "codeName", "type": ["null", "string"]}, {"default": null, "name": "isSystemDefined", "type": ["null", "boolean"]}]}