{"name": "SavingsAccountChargeDataV1", "namespace": "org.apache.fineract.avro.savings.v1", "type": "record", "fields": [{"default": null, "name": "id", "type": ["null", "long"]}, {"default": null, "name": "chargeId", "type": ["null", "long"]}, {"default": null, "name": "accountId", "type": ["null", "long"]}, {"default": null, "name": "name", "type": ["null", "string"]}, {"default": null, "name": "chargeTimeType", "type": ["null", "org.apache.fineract.avro.generic.v1.EnumOptionDataV1"]}, {"default": null, "name": "dueDate", "type": ["null", "string"]}, {"default": null, "name": "feeOnMonthDay", "type": ["null", "string"]}, {"default": null, "name": "feeInterval", "type": ["null", "int"]}, {"default": null, "name": "chargeCalculationType", "type": ["null", "org.apache.fineract.avro.generic.v1.EnumOptionDataV1"]}, {"default": null, "name": "percentage", "type": ["null", "bigdecimal"]}, {"default": null, "name": "amountPercentageAppliedTo", "type": ["null", "bigdecimal"]}, {"default": null, "name": "currency", "type": ["null", "org.apache.fineract.avro.generic.v1.CurrencyDataV1"]}, {"default": null, "name": "amount", "type": ["null", "bigdecimal"]}, {"default": null, "name": "amountPaid", "type": ["null", "bigdecimal"]}, {"default": null, "name": "amountWaived", "type": ["null", "bigdecimal"]}, {"default": null, "name": "amountWrittenOff", "type": ["null", "bigdecimal"]}, {"default": null, "name": "amountOutstanding", "type": ["null", "bigdecimal"]}, {"default": null, "name": "amountOrPercentage", "type": ["null", "bigdecimal"]}, {"default": null, "name": "penalty", "type": ["null", "boolean"]}, {"default": null, "name": "isActive", "type": ["null", "boolean"]}, {"default": null, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": ["null", "boolean"]}, {"default": null, "name": "freeWithdrawalChargeFrequency", "type": ["null", "int"]}, {"default": null, "name": "restartFrequency", "type": ["null", "int"]}, {"default": null, "name": "restartFrequencyEnum", "type": ["null", "int"]}, {"default": null, "name": "inactivationDate", "type": ["null", "string"]}, {"default": null, "name": "chargeData", "type": ["null", "org.apache.fineract.avro.portfolio.v1.ChargeDataV1"]}]}