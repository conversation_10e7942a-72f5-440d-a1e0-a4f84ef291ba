{"name": "LoanScheduleDataV1", "namespace": "org.apache.fineract.avro.loan.v1", "type": "record", "fields": [{"default": null, "name": "currency", "type": ["null", "org.apache.fineract.avro.generic.v1.CurrencyDataV1"]}, {"default": null, "name": "loanTermInDays", "type": ["null", "int"]}, {"default": null, "name": "totalPrincipalDisbursed", "type": ["null", "bigdecimal"]}, {"default": null, "name": "totalPrincipalExpected", "type": ["null", "bigdecimal"]}, {"default": null, "name": "totalPrincipalPaid", "type": ["null", "bigdecimal"]}, {"default": null, "name": "totalInterestCharged", "type": ["null", "bigdecimal"]}, {"default": null, "name": "totalFeeChargesCharged", "type": ["null", "bigdecimal"]}, {"default": null, "name": "totalPenaltyChargesCharged", "type": ["null", "bigdecimal"]}, {"default": null, "name": "totalWaived", "type": ["null", "bigdecimal"]}, {"default": null, "name": "totalWrittenOff", "type": ["null", "bigdecimal"]}, {"default": null, "name": "totalRepaymentExpected", "type": ["null", "bigdecimal"]}, {"default": null, "name": "totalRepayment", "type": ["null", "bigdecimal"]}, {"default": null, "name": "totalPaidInAdvance", "type": ["null", "bigdecimal"]}, {"default": null, "name": "totalPaidLate", "type": ["null", "bigdecimal"]}, {"default": null, "name": "totalOutstanding", "type": ["null", "bigdecimal"]}, {"default": null, "name": "periods", "type": ["null", {"type": "array", "items": "org.apache.fineract.avro.loan.v1.LoanSchedulePeriodDataV1"}]}, {"default": null, "name": "futurePeriods", "type": ["null", {"type": "array", "items": "org.apache.fineract.avro.loan.v1.LoanSchedulePeriodDataV1"}]}]}