{"name": "LoanProductInterestRecalculationDataV1", "namespace": "org.apache.fineract.avro.loan.v1", "type": "record", "fields": [{"default": null, "name": "id", "type": ["null", "long"]}, {"default": null, "name": "productId", "type": ["null", "long"]}, {"default": null, "name": "interestRecalculationCompoundingType", "type": ["null", "org.apache.fineract.avro.generic.v1.EnumOptionDataV1"]}, {"default": null, "name": "rescheduleStrategyType", "type": ["null", "org.apache.fineract.avro.generic.v1.EnumOptionDataV1"]}, {"default": null, "name": "recalculationRestFrequencyType", "type": ["null", "org.apache.fineract.avro.generic.v1.EnumOptionDataV1"]}, {"default": null, "name": "recalculationRestFrequencyInterval", "type": ["null", "int"]}, {"default": null, "name": "recalculationRestFrequencyNthDay", "type": ["null", "org.apache.fineract.avro.generic.v1.EnumOptionDataV1"]}, {"default": null, "name": "recalculationRestFrequencyWeekday", "type": ["null", "org.apache.fineract.avro.generic.v1.EnumOptionDataV1"]}, {"default": null, "name": "recalculationRestFrequencyOnDay", "type": ["null", "int"]}, {"default": null, "name": "recalculationCompoundingFrequencyType", "type": ["null", "org.apache.fineract.avro.generic.v1.EnumOptionDataV1"]}, {"default": null, "name": "recalculationCompoundingFrequencyInterval", "type": ["null", "int"]}, {"default": null, "name": "recalculationCompoundingFrequencyNthDay", "type": ["null", "org.apache.fineract.avro.generic.v1.EnumOptionDataV1"]}, {"default": null, "name": "recalculationCompoundingFrequencyWeekday", "type": ["null", "org.apache.fineract.avro.generic.v1.EnumOptionDataV1"]}, {"default": null, "name": "recalculationCompoundingFrequencyOnDay", "type": ["null", "int"]}, {"default": null, "name": "isArrearsBasedOnOriginalSchedule", "type": ["null", "boolean"]}, {"default": null, "name": "isCompoundingToBePostedAsTransaction", "type": ["null", "boolean"]}, {"default": null, "name": "preClosureInterestCalculationStrategy", "type": ["null", "org.apache.fineract.avro.generic.v1.EnumOptionDataV1"]}, {"default": null, "name": "allowCompoundingOnEod", "type": ["null", "boolean"]}]}