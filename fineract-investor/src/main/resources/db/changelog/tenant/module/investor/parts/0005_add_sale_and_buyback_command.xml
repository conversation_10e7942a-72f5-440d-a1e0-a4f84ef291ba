<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements. See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership. The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied. See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet id="1" author="fineract">
        <insert tableName="m_permission">
            <column name="grouping" value="transaction_loan"/>
            <column name="code" value="SALE_LOAN"/>
            <column name="entity_name" value="LOAN"/>
            <column name="action_name" value="SALE"/>
            <column name="can_maker_checker" valueBoolean="false"/>
        </insert>
        <insert tableName="m_permission">
            <column name="grouping" value="transaction_loan"/>
            <column name="code" value="BUYBACK_LOAN"/>
            <column name="entity_name" value="LOAN"/>
            <column name="action_name" value="BUYBACK"/>
            <column name="can_maker_checker" valueBoolean="false"/>
        </insert>
    </changeSet>
</databaseChangeLog>
