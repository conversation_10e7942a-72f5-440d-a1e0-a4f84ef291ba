<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements. See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership. The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied. See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="fineract" id="1" context="mysql">
        <createIndex tableName="m_external_asset_owner_transfer" indexName="external_asset_owner_transfer_loan_id">
            <column name="loan_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="fineract" id="1" context="postgresql">
        <createIndex tableName="m_external_asset_owner_transfer" indexName="external_asset_owner_transfer_loan_id">
            <column name="loan_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
