# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements. See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership. The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License. You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied. See the License for the
# specific language governing permissions and limitations
# under the License.
#


apiVersion: v1
kind: Service
metadata:
  labels:
    app: fineract-server
  name: fineract-server
spec:
  ports:
  - protocol: TCP
    port: 8443
    targetPort: 8443
  selector:
    app: fineract-server
    tier: backend
  type: LoadBalancer

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: fineract-server
  labels:
    app: fineract-server
spec:
  selector:
    matchLabels:
      app: fineract-server
      tier: backend
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: fineract-server
        tier: backend
    spec:
      initContainers:
        - name: init-mydb
          image: busybox:1.28
          command: ['sh', '-c', 'echo -e "Checking for the availability of MYSQL server deployment"; while ! nc -z "fineractmysql" 3306; do sleep 1; printf "-"; done; echo -e " >> MYSQL server has started";']
      containers:
      - name: fineract-server
        image: apache/fineract:latest
        resources:
          limits:
            cpu: "1000m"
            memory: "1Gi"
          requests:
            cpu: "200m"
            memory: "0.5Gi"
        livenessProbe:
          httpGet:
            path: /fineract-provider/actuator/health/liveness
            port: 8080
          initialDelaySeconds: 180
          periodSeconds: 1
        readinessProbe:
          httpGet:
            path: /fineract-provider/actuator/health/readiness
            port: 8080
          initialDelaySeconds: 180
          periodSeconds: 1
        env:
        - name: FINERACT_NODE_ID
          value: '1'
        - name: FINERACT_HIKARI_DRIVER_CLASS_NAME
          value: org.mariadb.jdbc.Driver
        - name: FINERACT_HIKARI_JDBC_URL
          value: **************************************************
        - name: FINERACT_HIKARI_USERNAME
          valueFrom:
            secretKeyRef:
              name: fineract-tenants-db-secret
              key: username
        - name: FINERACT_HIKARI_PASSWORD
          valueFrom:
            secretKeyRef:
              name: fineract-tenants-db-secret
              key: password
        - name: FINERACT_DEFAULT_TENANTDB_HOSTNAME
          value: fineractmysql
        - name: FINERACT_DEFAULT_TENANTDB_PORT
          value: '3306'
        - name: FINERACT_DEFAULT_TENANTDB_UID
          valueFrom:
            secretKeyRef:
              name: fineract-tenants-db-secret
              key: username
        - name: FINERACT_DEFAULT_TENANTDB_PWD
          valueFrom:
            secretKeyRef:
              name: fineract-tenants-db-secret
              key: password
        - name: FINERACT_DEFAULT_TENANTDB_CONN_PARAMS
          value: ''
        - name: JAVA_TOOL_OPTIONS
          value: '-Xmx1G'
        ports:
        - containerPort: 8443
          name: fineract-server
