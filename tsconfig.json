{"compileOnSave": false, "compilerOptions": {"baseUrl": "src", "downlevelIteration": true, "importHelpers": true, "module": "es2020", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "noImplicitAny": true, "suppressImplicitAnyIndexErrors": true, "target": "es2020", "typeRoots": ["node_modules/@types", "src/typings.d.ts"], "lib": ["es2017", "dom"]}}