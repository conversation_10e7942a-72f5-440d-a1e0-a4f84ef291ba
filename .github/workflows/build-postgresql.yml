name: Fineract Build & Test - PostgreSQL
on: [push, pull_request]

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-22.04

    services:
        postgresql:
            image: postgres:16.1
            ports:
                - 5432:5432
            env:
              POSTGRES_USER: root
              POSTGRES_PASSWORD: postgres
            options: --health-cmd="pg_isready -q -d postgres -U root" --health-interval=5s --health-timeout=2s --health-retries=3

        mock-oauth2-server:
          image: ghcr.io/navikt/mock-oauth2-server:2.1.0
          ports:
            - 9000:9000
          env:
            SERVER_PORT: 9000
            JSON_CONFIG: '{ "interactiveLogin": true, "httpServer": "NettyWrapper", "tokenCallbacks": [ { "issuerId": "auth/realms/fineract", "tokenExpiry": 120, "requestMappings": [{ "requestParam": "scope", "match": "fineract", "claims": { "sub": "mifos", "scope": [ "test" ] } } ] } ] }'

    env:
        TZ: Asia/Kolkata
        GRADLE_ENTERPRISE_ACCESS_KEY: ${{ secrets.GE_ACCESS_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4
        with:
          fetch-depth: 0
      - name: Set up JDK 17
        uses: actions/setup-java@387ac29b308b003ca37ba93a6cab5eb57c8f5f93 # v4
        with:
          java-version: '17'
          distribution: 'zulu'
          cache: gradle
      - uses: actions/setup-node@b39b52d1213e96004bfcb1c61a8a6fa8ab84f3e8 # v4
        with:
          node-version: 16
      - name: Congfigure vega-cli
        run: npm i -g vega-cli --unsafe
      - name: Validate Gradle wrapper
        uses: gradle/wrapper-validation-action@342dbebe7272035434f9baccc29a816ec6dd2c7b
      - name: Verify PostgreSQL connection
        run: |
            while ! pg_isready -d postgres -U root -h 127.0.0.1 -p 5432 ; do
                sleep 1
            done
      - name: Initialise databases
        run: |
            ./gradlew --no-daemon -q createPGDB -PdbName=fineract_tenants
            ./gradlew --no-daemon -q createPGDB -PdbName=fineract_default
      - name: Start LocalStack
        env:
          AWS_ENDPOINT_URL: http://localhost:4566
          AWS_ACCESS_KEY_ID: localstack
          AWS_SECRET_ACCESS_KEY: localstack
          AWS_REGION: us-east-1
        run: |
          docker run -d --name localstack -p 4566:4566 -p 4510-4559:4510-4559 localstack/localstack:2.1
          sleep 10
          docker exec localstack awslocal s3api create-bucket --bucket fineract-reports
          echo "LocalStack initialization complete"
      - name: Install additional software
        run: |
            sudo apt-get update
            sudo apt-get install ghostscript graphviz -y

      - name: Build & Test
        env:
          AWS_ENDPOINT_URL: http://localhost:4566
          AWS_ACCESS_KEY_ID: localstack
          AWS_SECRET_ACCESS_KEY: localstack
          AWS_REGION: us-east-1
          FINERACT_REPORT_EXPORT_S3_ENABLED: true
          FINERACT_REPORT_EXPORT_S3_BUCKET_NAME: fineract-reports
        run: |
            ./gradlew --no-daemon --console=plain build -x cucumber -x test -x doc
            ./gradlew --no-daemon --console=plain cucumber
            ./gradlew --no-daemon --console=plain test -x :twofactor-tests:test -x :oauth2-test:test -PdbType=postgresql
            ./gradlew --no-daemon --console=plain :twofactor-tests:test -PdbType=postgresql
            ./gradlew --no-daemon --console=plain :oauth2-tests:test -PdbType=postgresql

      - name: Archive test results
        if: always()
        uses: actions/upload-artifact@c7d193f32edcb7bfad88892161225aeda64e9392 # tag=v3
        with:
          name: test-results
          path: |
            build/reports/
            integration-tests/build/reports/
            twofactor-tests/build/reports/
            oauth2-tests/build/reports/

      - name: Archive server logs
        if: always()
        uses: actions/upload-artifact@c7d193f32edcb7bfad88892161225aeda64e9392 # tag=v3
        with:
          name: server-logs
          path: |
            integration-tests/build/cargo/
            twofactor-tests/build/cargo/
            oauth2-tests/build/cargo/
