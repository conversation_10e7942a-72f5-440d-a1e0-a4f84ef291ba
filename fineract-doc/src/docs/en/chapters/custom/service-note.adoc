= Note Service

The Note service is responsible for ... TBD

NOTE: We chose the note service because it's interface is very simple and has not many cross dependencies.

== Interfaces

.Note Read Service Interface
[source,java]
----
include::{rootdir}/fineract-provider/src/main/java/org/apache/fineract/portfolio/note/service/NoteReadPlatformService.java[lines=19..]
----

.Note Write Service Interface
[source,java]
----
include::{rootdir}/fineract-provider/src/main/java/org/apache/fineract/portfolio/note/service/NoteReadPlatformService.java[lines=19..]
----

== Auto Start Configuration

The rules to replace the Note services are very simple. If you provide an alternative implementation of the services then the default implementations will *not* be loaded.

.Note Auto Starter Configuration
[source,java]
----
include::{rootdir}/fineract-provider/src/main/java/org/apache/fineract/portfolio/note/starter/NoteAutoConfiguration.java[lines=19..]
----
