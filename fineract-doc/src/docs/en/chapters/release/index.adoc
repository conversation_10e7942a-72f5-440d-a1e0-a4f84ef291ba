= Releases

https://cwiki.apache.org/confluence/x/DRwIB[How to Release Apache Fineract] documents the process how we make the source code that is available here in this Git repository into a binary release tar.gz available on http://fineract.apache.org.

.Release Schedule
[plantuml, format=svg, width=100%]
----
include::{diagramsdir}/release-schedule.puml[]
----

include::configuration.adoc[leveloffset=+1]

include::process.adoc[leveloffset=+1]

include::maintenance.adoc[leveloffset=+1]

include::publish.adoc[leveloffset=+1]
