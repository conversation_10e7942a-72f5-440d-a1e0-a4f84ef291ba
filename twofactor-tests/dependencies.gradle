/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
dependencies {
    // testCompile dependencies are ONLY used in src/test, not src/main.
    // Do NOT repeat dependencies which are ALREADY in implementation or runtimeOnly!
    //
    tomcat 'org.apache.tomcat:tomcat:10.1.17@zip'
    testImplementation( files("$rootDir/fineract-provider/build/classes/java/main/"),
            project(path: ':fineract-provider', configuration: 'runtimeElements'),
            'org.junit.jupiter:junit-jupiter-api',
            'com.icegreen:greenmail-junit5',
            'org.bouncycastle:bcpkix-jdk15to18',
            'org.bouncycastle:bcprov-jdk15to18',
            )
    testImplementation ('io.rest-assured:rest-assured') {
        exclude group: 'commons-logging'
        exclude group: 'org.apache.sling'
        exclude group: 'com.sun.xml.bind'
    }
    testRuntimeOnly(
            'org.junit.jupiter:junit-jupiter-engine'
            )
}
