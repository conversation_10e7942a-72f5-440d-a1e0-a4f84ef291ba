/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.infrastructure.reportmailingjob.api;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.ZonedDateTime;

/**
 * Created by sanyam on 13/8/17.
 */
final class ReportMailingJobRunHistoryApiResourceSwagger {

    private ReportMailingJobRunHistoryApiResourceSwagger() {

    }

    @Schema(description = "GetReportMailingJobRunHistoryResponse")
    public static final class GetReportMailingJobRunHistoryResponse {

        private GetReportMailingJobRunHistoryResponse() {

        }

        @Schema(example = "1")
        public Long id;
        @Schema(example = "1")
        public Long reportMailingJobId;
        @Schema(example = "1469627093050")
        public ZonedDateTime startDateTime;
        @Schema(example = "1469627093050")
        public ZonedDateTime endDateTime;
        @Schema(example = "success")
        public String status;
        @Schema(example = "")
        public String errorMessage;
        @Schema(example = "")
        public String errorLog;
    }
}
