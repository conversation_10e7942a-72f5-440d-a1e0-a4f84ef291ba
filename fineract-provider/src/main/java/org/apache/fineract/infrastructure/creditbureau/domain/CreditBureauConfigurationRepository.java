/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.infrastructure.creditbureau.domain;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface CreditBureauConfigurationRepository
        extends JpaRepository<CreditBureauConfiguration, Long>, JpaSpecificationExecutor<CreditBureauConfiguration> {

    @Query("SELECT creditBureauConfig from CreditBureauConfiguration creditBureauConfig where creditBureauConfig.organisationCreditbureau.id = :creditBureauID and creditBureauConfig.configurationKey = :configurationKey ")
    CreditBureauConfiguration getCreditBureauConfigData(@Param("creditBureauID") Integer creditBureauID,
            @Param("configurationKey") String parameterName);

    @Query("SELECT creditBureauConfig from CreditBureauConfiguration creditBureauConfig where creditBureauConfig.organisationCreditbureau.id = :creditBureauID")
    CreditBureauConfiguration getCreditBureauConfigurationData(@Param("creditBureauID") Integer creditBureauID);

    @Query("SELECT creditBureauConfig from CreditBureauConfiguration creditBureauConfig where creditBureauConfig.configurationKey = :configurationKey")
    CreditBureauConfiguration getCreditBureauConfigurationValueData(@Param("configurationKey") String parameterName);

}
