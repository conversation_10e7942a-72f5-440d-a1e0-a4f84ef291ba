/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.infrastructure.bulkimport.importhandler.monthlysavings;

import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.fineract.commands.domain.CommandWrapper;
import org.apache.fineract.commands.service.CommandWrapperBuilder;
import org.apache.fineract.commands.service.PortfolioCommandSourceWritePlatformService;
import org.apache.fineract.infrastructure.bulkimport.constants.SaccoTransactionConstants;
import org.apache.fineract.infrastructure.bulkimport.constants.TemplatePopulateImportConstants;
import org.apache.fineract.infrastructure.bulkimport.data.Count;
import org.apache.fineract.infrastructure.bulkimport.importhandler.ImportHandler;
import org.apache.fineract.infrastructure.bulkimport.importhandler.ImportHandlerUtils;
import org.apache.fineract.infrastructure.bulkimport.importhandler.helper.DateSerializer;
import org.apache.fineract.infrastructure.bulkimport.importhandler.helper.SavingsAccountTransactionEnumValueSerialiser;
import org.apache.fineract.infrastructure.core.data.ApiParameterError;
import org.apache.fineract.infrastructure.core.exception.PlatformApiDataValidationException;
import org.apache.fineract.infrastructure.core.serialization.GoogleGsonSerializerHelper;
import org.apache.fineract.infrastructure.core.service.DateUtils;
import org.apache.fineract.portfolio.client.domain.Client;
import org.apache.fineract.portfolio.client.domain.ClientRepository;
import org.apache.fineract.portfolio.sacco.savingsaccount.data.SaccoSavingsTransactionImportData;
import org.apache.fineract.portfolio.savings.data.SavingsAccountTransactionEnumData;
import org.apache.fineract.portfolio.savings.domain.SavingsAccount;
import org.apache.fineract.portfolio.savings.domain.SavingsAccountRepository;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class SaccoSavingsTransactionImportHandler implements ImportHandler {

    private final ClientRepository clientRepository;
    private final SavingsAccountRepository savingsAccountRepository;
    private final PortfolioCommandSourceWritePlatformService commandsSourceWritePlatformService;

    @Override
    public Count process(final Workbook workbook, final String locale, final String dateFormat) {
        List<SaccoSavingsTransactionImportData> savingsTransactions = readExcelFile(workbook, locale, dateFormat);
        return importEntity(workbook, savingsTransactions, dateFormat);
    }

    private List<SaccoSavingsTransactionImportData> readExcelFile(final Workbook workbook, final String locale, final String dateFormat) {
        List<SaccoSavingsTransactionImportData> savingsTransactions = new ArrayList<>();
        Sheet savingsTransactionSheet = workbook.getSheet(TemplatePopulateImportConstants.SAVINGS_TRANSACTION_SHEET_NAME);
        Integer noOfEntries = ImportHandlerUtils.getNumberOfRows(savingsTransactionSheet, 0);
        for (int rowIndex = 1; rowIndex <= noOfEntries; rowIndex++) {
            Row row;
            row = savingsTransactionSheet.getRow(rowIndex);
            try {
                if (ImportHandlerUtils.isNotImported(row, SaccoTransactionConstants.STATUS_COL)) {
                    savingsTransactions.addAll(readSavingsTransaction(workbook, row, locale, dateFormat));
                }
            } catch (Throwable ignored) {
                log.error("sacco error {}", ignored.getMessage(), ignored);
            }
        }
        return savingsTransactions;
    }

    private List<SaccoSavingsTransactionImportData> readSavingsTransaction(final Workbook workbook, final Row row, final String locale,
            final String dateFormat) {
        String clientAccountNumber = ImportHandlerUtils.readAsString(SaccoTransactionConstants.CLIENT_ACCOUNT_NO_COL, row);

        if (StringUtils.isBlank(clientAccountNumber)) {
            return Collections.emptyList();
        }

        Client client = clientRepository.findByAccountNumber(clientAccountNumber).orElse(null);
        // .orElseThrow(() -> new RuntimeException("Client not found"));

        String savingsAccountIdCheck = ImportHandlerUtils.readAsString(SaccoTransactionConstants.SAVINGS_ACCOUNT_NO_COL, row);
        SavingsAccount account = null;
        if (client != null) {
            account = client.getSavingsAccountId() != null ? savingsAccountRepository.findById(client.getSavingsAccountId()).orElse(null)
                    : null;
        }
        if (StringUtils.isNotBlank(savingsAccountIdCheck)) {
            account = savingsAccountRepository.findSavingsAccountByAccountNumber(savingsAccountIdCheck);
        } else if (account == null && client != null) {
            account = savingsAccountRepository.findSavingAccountByClientId(client.getId()).stream().filter(SavingsAccount::isActive)
                    .findFirst().orElseThrow(() -> new RuntimeException(
                            String.format("Failed to find savings account with account number %s", savingsAccountIdCheck)));
        }

        BigDecimal amount = null;
        if (ImportHandlerUtils.readAsDouble(SaccoTransactionConstants.AMOUNT_COL, row) != null) {
            amount = BigDecimal.valueOf(ImportHandlerUtils.readAsDouble(SaccoTransactionConstants.AMOUNT_COL, row));
        }

        LocalDate startDate = ImportHandlerUtils.readAsDate(SaccoTransactionConstants.START_TRANSACTION_DATE_COL, row);
        if (startDate == null) {
            throw new RuntimeException("start date is null");
        }
        LocalDate endDate = ImportHandlerUtils.readAsDate(SaccoTransactionConstants.END_TRANSACTION_DATE_COL, row);

        String paymentType = ImportHandlerUtils.readAsString(SaccoTransactionConstants.PAYMENT_TYPE_COL, row);
        Long paymentTypeId = ImportHandlerUtils.getIdByName(workbook.getSheet(TemplatePopulateImportConstants.EXTRAS_SHEET_NAME),
                paymentType);
        String accountNumber = ImportHandlerUtils.readAsString(SaccoTransactionConstants.ACCOUNT_NO_COL, row);
        String checkNumber = ImportHandlerUtils.readAsString(SaccoTransactionConstants.CHECK_NO_COL, row);
        String routingCode = ImportHandlerUtils.readAsString(SaccoTransactionConstants.ROUTING_CODE_COL, row);
        String receiptNumber = ImportHandlerUtils.readAsString(SaccoTransactionConstants.RECEIPT_NO_COL, row);
        String bankNumber = ImportHandlerUtils.readAsString(SaccoTransactionConstants.BANK_NO_COL, row);

        List<SaccoSavingsTransactionImportData> transactions = new ArrayList<>();

        if (endDate == null) {
            transactions.add(SaccoSavingsTransactionImportData.builder().accountId(account != null ? account.getId() : null)
                    .transactionAmount(amount).transactionDate(startDate).paymentTypeId(paymentTypeId).accountNumber(accountNumber)
                    .checkNumber(checkNumber).routingCode(routingCode).receiptNumber(receiptNumber).bankNumber(bankNumber).locale(locale)
                    .dateFormat(dateFormat).rowIndex(row.getRowNum()).build());
            return transactions;
        }

        while (DateUtils.isBefore(startDate, endDate) || DateUtils.isEqual(startDate, endDate)) {
            transactions.add(SaccoSavingsTransactionImportData.builder().accountId(account != null ? account.getId() : null)
                    .transactionAmount(amount).transactionDate(startDate).paymentTypeId(paymentTypeId).accountNumber(accountNumber)
                    .checkNumber(checkNumber).routingCode(routingCode).receiptNumber(receiptNumber).bankNumber(bankNumber).locale(locale)
                    .dateFormat(dateFormat).rowIndex(row.getRowNum()).build());
            startDate = startDate.plusMonths(1);
        }

        return transactions;
    }

    private Count importEntity(final Workbook workbook, final List<SaccoSavingsTransactionImportData> savingsTransactions,
            final String dateFormat) {
        Sheet savingsTransactionSheet = workbook.getSheet(TemplatePopulateImportConstants.SAVINGS_TRANSACTION_SHEET_NAME);
        int successCount = 0;
        int errorCount = 0;
        String errorMessage = "";
        GsonBuilder gsonBuilder = GoogleGsonSerializerHelper.createGsonBuilder();
        gsonBuilder.registerTypeAdapter(LocalDate.class, new DateSerializer(dateFormat));
        gsonBuilder.registerTypeAdapter(SavingsAccountTransactionEnumData.class, new SavingsAccountTransactionEnumValueSerialiser());

        for (SaccoSavingsTransactionImportData transaction : savingsTransactions) {
            try {
                JsonObject savingsTransactionJsonob = gsonBuilder.create().toJsonTree(transaction).getAsJsonObject();
                savingsTransactionJsonob.remove("accountId");
                savingsTransactionJsonob.remove("rowIndex");
                String payload = savingsTransactionJsonob.toString();
                CommandWrapper commandRequest = new CommandWrapperBuilder() //
                        .saccoSavingsAccountDeposit(transaction.getAccountId()) //
                        .withJson(payload) //
                        .build();
                commandsSourceWritePlatformService.logCommandSource(commandRequest);
                successCount++;
                Cell statusCell = savingsTransactionSheet.getRow(transaction.getRowIndex())
                        .createCell(SaccoTransactionConstants.STATUS_COL);
                statusCell.setCellValue(TemplatePopulateImportConstants.STATUS_CELL_IMPORTED);
                statusCell.setCellStyle(ImportHandlerUtils.getCellStyle(workbook, IndexedColors.LIGHT_GREEN));
            } catch (PlatformApiDataValidationException ex) {
                errorCount++;
                log.error("Problem occurred in importEntity function", ex);
                if (ex.getErrors() != null && !ex.getErrors().isEmpty()) {
                    errorMessage = String.join(",", ex.getErrors().stream().map(ApiParameterError::getDefaultUserMessage).toList());
                    ImportHandlerUtils.writeErrorMessage(savingsTransactionSheet, transaction.getRowIndex(), errorMessage,
                            SaccoTransactionConstants.STATUS_COL);
                } else {
                    errorMessage = ImportHandlerUtils.getErrorMessage(ex);
                    ImportHandlerUtils.writeErrorMessage(savingsTransactionSheet, transaction.getRowIndex(), errorMessage,
                            SaccoTransactionConstants.STATUS_COL);
                }
            } catch (RuntimeException ex) {
                errorCount++;
                log.error("Problem occurred in importEntity function", ex);
                errorMessage = ImportHandlerUtils.getErrorMessage(ex);
                ImportHandlerUtils.writeErrorMessage(savingsTransactionSheet, transaction.getRowIndex(), errorMessage,
                        SaccoTransactionConstants.STATUS_COL);
            }
        }
        return Count.instance(successCount, errorCount);
    }

    private void writeLoanErrorMessage(final Workbook workbook, final String errorMessage, final Cell statusCell,
            final Cell errorReportCell) {

        statusCell.setCellStyle(ImportHandlerUtils.getCellStyle(workbook, IndexedColors.RED));
        errorReportCell.setCellValue(errorMessage);
    }

}
