package org.apache.fineract.portfolio.sacco.monthlysavings.domain;

import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface MonthlySavingsAccountRepository extends JpaRepository<MonthlySavingsAccount, Long> {

    @Query("select m from MonthlySavingsAccount m where m.saccoSavingsAccount.savingsAccount.id = ?1")
    Optional<MonthlySavingsAccount> findBySavingsAccountId(Long id);
}
