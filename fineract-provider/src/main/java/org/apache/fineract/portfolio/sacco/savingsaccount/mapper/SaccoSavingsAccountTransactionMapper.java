package org.apache.fineract.portfolio.sacco.savingsaccount.mapper;

import org.apache.fineract.portfolio.sacco.savingsaccount.data.AccountTransaction;
import org.apache.fineract.portfolio.sacco.savingsaccount.data.SaccoSavingsAccountTransaction;
import org.apache.fineract.portfolio.savings.domain.SavingsAccountTransaction;
import org.apache.fineract.portfolio.shareaccounts.domain.ShareAccountTransaction;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface SaccoSavingsAccountTransactionMapper {

    SaccoSavingsAccountTransaction toBo(
            org.apache.fineract.portfolio.sacco.savingsaccount.domain.SaccoSavingsAccountTransaction transaction);

    AccountTransaction toBo(ShareAccountTransaction transaction);

    AccountTransaction toBo(SavingsAccountTransaction transaction);
}
