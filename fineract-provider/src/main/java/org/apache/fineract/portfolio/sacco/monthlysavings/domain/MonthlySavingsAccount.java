package org.apache.fineract.portfolio.sacco.monthlysavings.domain;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import lombok.*;
import org.apache.fineract.infrastructure.core.domain.AbstractPersistableCustom;
import org.apache.fineract.portfolio.sacco.savingsaccount.domain.SaccoSavingsAccount;

@Entity
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "a_m_monthly_savings_account")
public class MonthlySavingsAccount extends AbstractPersistableCustom {

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sacco_savings_account_id", nullable = false)
    private SaccoSavingsAccount saccoSavingsAccount;

    @OneToMany(mappedBy = "monthlySavingsAccount", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private List<MonthlySavingsAccountDetail> details;

    @Column(name = "last_paid_date")
    private LocalDate lastPaidDate;

    @Builder.Default
    @Column(name = "last_transaction_date")
    private LocalDate lastTransactionDate = LocalDate.now();

    @Builder.Default
    @Column(name = "penalty_amount")
    private BigDecimal penaltyAmount = BigDecimal.ZERO;

    @Builder.Default
    @Column(name = "unpaid_amount")
    private BigDecimal unpaidAmount = BigDecimal.ZERO;

    @Builder.Default
    @Column(name = "remainder_amount")
    private BigDecimal remainderAmount = BigDecimal.ZERO;
}
