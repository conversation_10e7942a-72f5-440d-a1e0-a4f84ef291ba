/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.fineract.portfolio.loanaccount.data;

import jakarta.persistence.Transient;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.fineract.infrastructure.core.data.EnumOptionData;
import org.apache.fineract.infrastructure.core.domain.ExternalId;
import org.apache.fineract.infrastructure.dataqueries.data.DatatableData;
import org.apache.fineract.organisation.monetary.data.CurrencyData;
import org.apache.fineract.portfolio.account.data.PortfolioAccountData;
import org.apache.fineract.portfolio.accountdetails.data.LoanAccountSummaryData;
import org.apache.fineract.portfolio.calendar.data.CalendarData;
import org.apache.fineract.portfolio.charge.data.ChargeData;
import org.apache.fineract.portfolio.collateralmanagement.data.LoanCollateralManagementData;
import org.apache.fineract.portfolio.delinquency.data.DelinquencyRangeData;
import org.apache.fineract.portfolio.floatingrates.data.InterestRatePeriodData;
import org.apache.fineract.portfolio.group.data.GroupGeneralData;
import org.apache.fineract.portfolio.loanaccount.guarantor.data.GuarantorData;
import org.apache.fineract.portfolio.loanaccount.loanschedule.data.LoanScheduleData;
import org.apache.fineract.portfolio.loanproduct.data.LoanProductData;
import org.apache.fineract.portfolio.note.data.NoteData;
import org.apache.fineract.portfolio.rate.data.RateData;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@SuppressWarnings("ObjectToString")
public class LoanAccountImportData {

    // basic loan details

    // identity
    private Long id;
    private String accountNo;
    private ExternalId externalId = ExternalId.empty();

    // status
    private LoanStatusEnumData status;
    private EnumOptionData subStatus;

    // related to
    private Long clientId;
    private String clientAccountNo;
    private String clientName;
    private ExternalId clientExternalId;
    private Long clientOfficeId;
    private GroupGeneralData group;
    private Long loanProductId;
    private String loanProductName;
    private String loanProductDescription;
    // TODO: avoid prefix "is"
    private boolean isLoanProductLinkedToFloatingRate;
    private Long fundId;
    private String fundName;
    private Long loanPurposeId;
    private String loanPurposeName;
    private Long loanOfficerId;
    private String loanOfficerName;
    private String loanType;

    // terms
    private CurrencyData currency;
    private BigDecimal principal;
    private BigDecimal approvedPrincipal;
    private BigDecimal proposedPrincipal;
    private BigDecimal netDisbursalAmount;

    private Integer termFrequency;
    private Integer termPeriodFrequencyType;
    private Integer numberOfRepayments;
    private Integer repaymentEvery;
    private Integer repaymentFrequencyType;
    private Integer repaymentFrequencyNthDayType;
    private Integer repaymentFrequencyDayOfWeekType;
    private BigDecimal interestRatePerPeriod;
    private Integer interestRateFrequencyType;
    private BigDecimal annualInterestRate;
    // TODO: avoid prefix "is"
    private boolean isFloatingInterestRate;
    private BigDecimal interestRateDifferential;

    // settings
    private Integer amortizationType;
    private Integer interestType;
    private Integer interestCalculationPeriodType;
    private Boolean allowPartialPeriodInterestCalculation;
    private BigDecimal inArrearsTolerance;
    private String transactionProcessingStrategyCode;
    private String transactionProcessingStrategyName;
    private Integer graceOnPrincipalPayment;
    private Integer recurringMoratoriumOnPrincipalPeriods;
    private Integer graceOnInterestPayment;
    private Integer graceOnInterestCharged;
    private Integer graceOnArrearsAgeing;
    private LocalDate interestChargedFromDate;
    private LocalDate expectedFirstRepaymentOnDate;
    private Boolean syncDisbursementWithMeeting;
    private Boolean disallowExpectedDisbursements;

    // timeline
    private LoanApplicationTimelineData timeline;

    // totals
    private LoanSummaryData summary;

    // associations
    private LoanScheduleData repaymentSchedule;
    private Collection<LoanTransactionData> transactions;
    private Collection<LoanChargeData> charges;
    private Collection<LoanCollateralManagementData> collateral;
    private Collection<GuarantorData> guarantors;
    private CalendarData meeting;
    private Collection<NoteData> notes;
    private Collection<DisbursementData> disbursementDetails;
    private LoanScheduleData originalSchedule;

    @Transient
    private BigDecimal feeChargesAtDisbursementCharged;
    private BigDecimal totalOverpaid;

    // loanCycle
    private Integer loanCounter;
    private Integer loanProductCounter;

    // linkable account details
    private PortfolioAccountData linkedAccount;
    private Collection<PortfolioAccountData> accountLinkingOptions;

    private Boolean multiDisburseLoan;

    private Boolean canDefineInstallmentAmount;

    private BigDecimal fixedEmiAmount;

    private BigDecimal maxOutstandingLoanBalance;

    private Boolean canDisburse;

    private Collection<LoanTermVariationsData> emiAmountVariations;
    private Collection<LoanAccountSummaryData> clientActiveLoanOptions;
    private Boolean canUseForTopup;
    // TODO: avoid prefix "is"
    private boolean isTopup;
    private boolean fraud;
    private Long closureLoanId;
    private String closureLoanAccountNo;
    private BigDecimal topupAmount;

    private LoanProductData product;

    private Map<Long, LoanBorrowerCycleData> memberVariations;

    private Boolean inArrears;
    // TODO: avoid prefix "is"
    private Boolean isNPA;
    private Collection<ChargeData> overdueCharges;

    private Integer daysInMonthType;
    private Integer daysInYearType;
    // TODO: avoid prefix "is"
    private boolean isInterestRecalculationEnabled;

    private LoanInterestRecalculationData interestRecalculationData;
    private Boolean createStandingInstructionAtDisbursement;

    // Paid In Advance
    private PaidInAdvanceData paidInAdvance;

    private Collection<InterestRatePeriodData> interestRatesPeriods;

    // VariableInstallments
    // TODO: avoid prefix "is"
    private Boolean isVariableInstallmentsAllowed;
    private Integer minimumGap;
    private Integer maximumGap;

    private List<DatatableData> datatables = null;
    // TODO: avoid prefix "is"
    private Boolean isEqualAmortization;
    private BigDecimal fixedPrincipalPercentagePerInstallment;

    // Rate
    private List<RateData> rates;
    // TODO: avoid prefix "is"
    private Boolean isRatesEnabled;

    // import fields
    private String dateFormat;
    private String locale;
    private transient Integer rowIndex;
    private LocalDate submittedOnDate;
    private Long productId;
    private Integer loanTermFrequency;
    private Integer loanTermFrequencyType;
    private LocalDate repaymentsStartingFromDate;
    private String linkAccountId;
    private Long groupId;
    private LocalDate expectedDisbursementDate;

    private LocalDate overpaidOnDate;
    private CollectionData delinquent;
    private DelinquencyRangeData delinquencyRange;
    private Boolean enableInstallmentLevelDelinquency;
    private LocalDate lastClosedBusinessDate;
    private Boolean chargedOff;

    private Boolean enableDownPayment;
    private BigDecimal disbursedAmountPercentageForDownPayment;
    private Boolean enableAutoRepaymentForDownPayment;
    private Boolean disableScheduleExtensionForDownPayment;

    private Integer loanScheduleType;
    private Integer loanScheduleProcessingType;

    public static LoanAccountImportData importInstanceIndividual(String loanTypeEnumOption, Long clientId, Long productId,
            Long loanOfficerId, LocalDate submittedOnDate, Long fundId, BigDecimal principal, Integer numberOfRepayments,
            Integer repaymentEvery, Integer repaidEveryFrequencyEnums, Integer loanTermFrequency, Integer loanTermFrequencyTypeEnum,
            BigDecimal nominalInterestRate, LocalDate expectedDisbursementDate, Integer amortizationEnumOption, Integer interestMethodEnum,
            Integer interestCalculationPeriodTypeEnum, BigDecimal inArrearsTolerance, String transactionProcessingStrategyCode,
            Integer graceOnPrincipalPayment, Integer graceOnInterestPayment, Integer graceOnInterestCharged,
            LocalDate interestChargedFromDate, LocalDate repaymentsStartingFromDate, Integer rowIndex, ExternalId externalId, Long groupId,
            Collection<LoanChargeData> charges, String linkAccountId, String locale, String dateFormat,
            List<LoanCollateralManagementData> loanCollateralManagementData) {

        return new LoanAccountImportData().setLoanType(loanTypeEnumOption).setClientId(clientId).setProductId(productId)
                .setLoanOfficerId(loanOfficerId).setSubmittedOnDate(submittedOnDate).setFundId(fundId).setPrincipal(principal)
                .setNumberOfRepayments(numberOfRepayments).setRepaymentEvery(repaymentEvery)
                .setRepaymentFrequencyType(repaidEveryFrequencyEnums).setLoanTermFrequency(loanTermFrequency)
                .setLoanTermFrequencyType(loanTermFrequencyTypeEnum).setInterestRatePerPeriod(nominalInterestRate)
                .setExpectedDisbursementDate(expectedDisbursementDate).setAmortizationType(amortizationEnumOption)
                .setInterestType(interestMethodEnum).setInterestCalculationPeriodType(interestCalculationPeriodTypeEnum)
                .setInArrearsTolerance(inArrearsTolerance).setTransactionProcessingStrategyCode(transactionProcessingStrategyCode)
                .setGraceOnPrincipalPayment(graceOnPrincipalPayment).setGraceOnInterestPayment(graceOnInterestPayment)
                .setGraceOnInterestCharged(graceOnInterestCharged).setInterestChargedFromDate(interestChargedFromDate)
                .setRepaymentsStartingFromDate(repaymentsStartingFromDate).setRowIndex(rowIndex).setExternalId(externalId)
                .setGroupId(groupId).setCharges(charges).setLinkAccountId(linkAccountId).setLocale(locale).setDateFormat(dateFormat)
                .setCollateral(loanCollateralManagementData);
    }

    public static LoanAccountImportData importInstanceGroup(String loanTypeEnumOption, Long groupIdforGroupLoan, Long productId,
            Long loanOfficerId, LocalDate submittedOnDate, Long fundId, BigDecimal principal, Integer numberOfRepayments,
            Integer repaidEvery, Integer repaidEveryFrequencyEnums, Integer loanTermFrequency, Integer loanTermFrequencyTypeEnum,
            BigDecimal nominalInterestRate, Integer amortizationEnumOption, Integer interestMethodEnum,
            Integer interestCalculationPeriodEnum, BigDecimal arrearsTolerance, String transactionProcessingStrategyCode,
            Integer graceOnPrincipalPayment, Integer graceOnInterestPayment, Integer graceOnInterestCharged,
            LocalDate interestChargedFromDate, LocalDate repaymentsStartingFromDate, Integer rowIndex, ExternalId externalId,
            String linkAccountId, String locale, String dateFormat) {

        return new LoanAccountImportData().setLoanType(loanTypeEnumOption).setGroupId(groupIdforGroupLoan).setProductId(productId)
                .setLoanOfficerId(loanOfficerId).setSubmittedOnDate(submittedOnDate).setFundId(fundId).setPrincipal(principal)
                .setNumberOfRepayments(numberOfRepayments).setRepaymentEvery(repaidEvery)
                .setRepaymentFrequencyType(repaidEveryFrequencyEnums).setLoanTermFrequency(loanTermFrequency)
                .setLoanTermFrequencyType(loanTermFrequencyTypeEnum).setInterestRatePerPeriod(nominalInterestRate)
                .setInterestType(interestMethodEnum).setInterestCalculationPeriodType(interestCalculationPeriodEnum)
                .setInArrearsTolerance(arrearsTolerance).setTransactionProcessingStrategyCode(transactionProcessingStrategyCode)
                .setGraceOnPrincipalPayment(graceOnPrincipalPayment).setGraceOnInterestPayment(graceOnInterestPayment)
                .setGraceOnInterestCharged(graceOnInterestCharged).setInterestChargedFromDate(interestChargedFromDate)
                .setRepaymentsStartingFromDate(repaymentsStartingFromDate).setRowIndex(rowIndex).setExternalId(externalId)
                .setLinkAccountId(linkAccountId).setLocale(locale).setDateFormat(dateFormat);
    }
}
