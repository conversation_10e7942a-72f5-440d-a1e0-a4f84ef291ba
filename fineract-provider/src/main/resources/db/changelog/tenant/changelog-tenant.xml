<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements. See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership. The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied. See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <include file="parts/0003_postgresql_specific_initial_data.xml" relativeToChangelogFile="true"/>
    <include file="parts/0004_camelcase_column_renaming.xml" relativeToChangelogFile="true"/>
    <include file="parts/0005_savings_transaction_reversal.xml" relativeToChangelogFile="true"/>
    <include file="parts/0006_product_loan_disallow_expected_disbursements.xml" relativeToChangelogFile="true"/>
    <include file="parts/0007_product_loan_higher_than_applied_loan_amount_management.xml" relativeToChangelogFile="true"/>
    <include file="parts/0008_loan_charge_add_external_id.xml" relativeToChangelogFile="true"/>
    <include file="parts/0009_hold_reason_savings_account.xml" relativeToChangelogFile="true"/>
    <include file="parts/0010_lien_allowed_on_savings_account_products.xml" relativeToChangelogFile="true"/>
    <include file="parts/0011_add_credit_balance_refund_permission.xml" relativeToChangelogFile="true"/>
    <include file="parts/0012_add_merchantissuedrefund_payoutrefund_goodwillcredit_permissions.xml" relativeToChangelogFile="true"/>
    <include file="parts/0013_remove_topics.xml" relativeToChangelogFile="true"/>
    <include file="parts/0014_remove_unused_jobs.xml" relativeToChangelogFile="true"/>
    <include file="parts/0015_add_business_date.xml" relativeToChangelogFile="true"/>
    <include file="parts/0016_changed_unique_constraint_of_ref_no.xml" relativeToChangelogFile="true"/>
    <include file="parts/0017_fix_stretchy_reports.xml" relativeToChangelogFile="true"/>
    <include file="parts/0018_pentaho_reports_to_table.xml" relativeToChangelogFile="true"/>
    <include file="parts/0019_refactor_loan_transaction.xml" relativeToChangelogFile="true"/>
    <include file="parts/0020_add_audit_entries.xml" relativeToChangelogFile="true"/>
    <include file="parts/0021_add_spring_batch_db_structure.xml" relativeToChangelogFile="true"/>
    <include file="parts/0022_add_batch_business_step_configuration_table.xml" relativeToChangelogFile="true"/>
    <include file="parts/0023_use_the_proper_date_or_datetime_type.xml" relativeToChangelogFile="true"/>
    <include file="parts/0024_add_audit_entries.xml" relativeToChangelogFile="true"/>
    <include file="parts/0025_add_audit_entries_to_journal_entry.xml" relativeToChangelogFile="true"/>
    <include file="parts/0026_reversals_for_reversed_transactions.xml" relativeToChangelogFile="true"/>
    <include file="parts/0027_add_charge_refund_permission.xml" relativeToChangelogFile="true"/>
    <include file="parts/0028_add_charge_refund_charge_type_to_loan_transaction.xml" relativeToChangelogFile="true"/>
    <include file="parts/0029_add_delinquency_buckets.xml" relativeToChangelogFile="true"/>
    <include file="parts/0030_add_audit_entries_to_business_date.xml" relativeToChangelogFile="true"/>
    <include file="parts/0031_add_audit_entries_to_client_identifier.xml" relativeToChangelogFile="true"/>
    <include file="parts/0032_add_some_missing_indexes.xml" relativeToChangelogFile="true"/>
    <include file="parts/0033_add_audit_entries_to_loan_repayment_schedule_installment.xml" relativeToChangelogFile="true"/>
    <include file="parts/0034_add_audit_entries_to_note.xml" relativeToChangelogFile="true"/>
    <include file="parts/0035_add_audit_entries_to_calendar.xml" relativeToChangelogFile="true"/>
    <include file="parts/0036_add_audit_entries_and_rework_command_source_datetime_fields.xml" relativeToChangelogFile="true"/>
    <include file="parts/0037_add_loan_cob_job_data.xml" relativeToChangelogFile="true"/>
    <include file="parts/0038_add_reversal_data_to_loan_transaction.xml" relativeToChangelogFile="true"/>
    <include file="parts/0039_add_loan_account_locks.xml" relativeToChangelogFile="true"/>
    <include file="parts/0040_add_delinquency_tags_job.xml" relativeToChangelogFile="true"/>
    <include file="parts/0041_add_update_business_step_permission.xml" relativeToChangelogFile="true"/>
    <include file="parts/0042_table_report_query_fix.xml" relativeToChangelogFile="true"/>
    <include file="parts/0043_add_external_event_table.xml" relativeToChangelogFile="true"/>
    <include file="parts/0044_table_report_query_fix.xml" relativeToChangelogFile="true"/>
    <include file="parts/0045_external_event_table_data_binary.xml" relativeToChangelogFile="true"/>
    <include file="parts/0046_external_event_table_schema_info.xml" relativeToChangelogFile="true"/>
    <include file="parts/0047_add_loan_delinquency_tags_business_step.xml" relativeToChangelogFile="true"/>
    <include file="parts/0048_rework_loan_account_locks.xml" relativeToChangelogFile="true"/>
    <include file="parts/0049_add_send_asynchronous_events_job.xml" relativeToChangelogFile="true"/>
    <include file="parts/0050_add_reverse_flag_disbursement_details.xml" relativeToChangelogFile="true"/>
    <include file="parts/0051_external_event_table_category_info.xml" relativeToChangelogFile="true"/>
    <include file="parts/0052_loan_transaction_chargeback.xml" relativeToChangelogFile="true"/>
    <include file="parts/0053_add_external_events_purge_job.xml" relativeToChangelogFile="true"/>
    <include file="parts/0054_additional_fields_loan_repayment_schedule.xml" relativeToChangelogFile="true"/>
    <include file="parts/0055_add_submitted_on_date_to_loan_charge.xml" relativeToChangelogFile="true"/>
    <include file="parts/0056_add_external_event_default_configuration.xml" relativeToChangelogFile="true"/>
    <include file="parts/0057_add_principal_adjustments_to_loan.xml" relativeToChangelogFile="true"/>
    <include file="parts/0058_add_execute_inline_cob_permission.xml" relativeToChangelogFile="true"/>
    <include file="parts/0059_add_spring_batch_loan_id_list_table.xml" relativeToChangelogFile="true"/>
    <include file="parts/0060_add_job_name_to_command_source.xml" relativeToChangelogFile="true"/>
    <include file="parts/0061_add_idempotency_key_to_command_source.xml" relativeToChangelogFile="true"/>
    <include file="parts/0062_add_fraud_attribute_to_loan.xml" relativeToChangelogFile="true"/>
    <include file="parts/0063_add_permissions_for_external_event_configuration.xml" relativeToChangelogFile="true"/>
    <include file="parts/0064_refactor_loan_transaction_strategy.xml" relativeToChangelogFile="true"/>
    <include file="parts/0065_add_bypass_loan_write_transaction_permission.xml" relativeToChangelogFile="true"/>
    <include file="parts/0066_delinquency_classification_chargeback.xml" relativeToChangelogFile="true"/>
    <include file="parts/0067_add_configurations_for_repayment_due_business_steps.xml" relativeToChangelogFile="true"/>
    <include file="parts/0068_loan_charge_adjustment.xml" relativeToChangelogFile="true"/>
    <include file="parts/0069_add_unique_constraint_for_reversal_external_id_of_loan_transactions.xml" relativeToChangelogFile="true"/>
    <include file="parts/0070_add_event_configuration_for_delinquency_range_change_event.xml" relativeToChangelogFile="true"/>
    <include file="parts/0071_add_external_id_support_for_loan_transaction.xml" relativeToChangelogFile="true"/>
    <include file="parts/0072_add_result_and status_to_command_source.xml" relativeToChangelogFile="true" />
    <include file="parts/0073_add_result_status_code_to_command_source.xml" relativeToChangelogFile="true" />
    <include file="parts/0074_add_last_cob_business_date_column_to_loan.xml" relativeToChangelogFile="true"/>
    <include file="parts/0075_add_processed_commands_purge_job.xml" relativeToChangelogFile="true" />
    <include file="parts/0076_add_loan_transaction_enum_values.xml" relativeToChangelogFile="true" />
    <include file="parts/0077_add_overpaid_date_for_loan.xml" relativeToChangelogFile="true" />
    <include file="parts/0078_add_configuration_cob_bulk_event.xml" relativeToChangelogFile="true" />
    <include file="parts/0079_add_charge_off_details_to_loan.xml" relativeToChangelogFile="true" />
    <include file="parts/0080_add_external_event_configuration_for_stayed_locked_loans_business_event.xml" relativeToChangelogFile="true" />
    <include file="parts/0081_add_configuration_event_producer_batch_size.xml" relativeToChangelogFile="true" />
    <include file="parts/0082_add_external_event_default_configuration.xml" relativeToChangelogFile="true" />
    <include file="parts/0083_add_loan_transaction_enum_values.xml" relativeToChangelogFile="true" />
    <include file="parts/0084_add_general_accounting_table_reports.xml" relativeToChangelogFile="true" />
    <include file="parts/0085_add_aggregate_root_id_external_events.xml" relativeToChangelogFile="true" />
    <include file="parts/0086_add_cob_business_date_to_loan_account_locks.xml" relativeToChangelogFile="true" />
    <include file="parts/0087_update_dashboard_table_reports.xml" relativeToChangelogFile="true" />
    <include file="parts/0088_drop_m_loan_transaction_version_column.xml" relativeToChangelogFile="true" />
    <include file="parts/0089_add_update_loan_arrears_aging_business_step.xml" relativeToChangelogFile="true" />
    <include file="parts/0090_add_report_export_s3_folder_configuration.xml" relativeToChangelogFile="true" />
    <include file="parts/0091_modify_parameter_value_type_in_job_parameters_table.xml" relativeToChangelogFile="true" />
    <include file="parts/0092_add_periodic_accrual_entries_business_step.xml" relativeToChangelogFile="true" />
    <include file="parts/0093_update_general_accounting_table_reports.xml" relativeToChangelogFile="true" />
    <include file="parts/0094_add_external_event_default_configuration.xml" relativeToChangelogFile="true" />
    <include file="parts/0095_add_delinquency_and_arrears_display_configuration.xml" relativeToChangelogFile="true" />
    <include file="parts/0096_modify_created_and_sent_at_date_external_events.xml" relativeToChangelogFile="true" />
    <include file="parts/0097_update_accounting_summary_table_reports.xml" relativeToChangelogFile="true" />
    <include file="parts/0098_update_transaction_summary_table_report.xml" relativeToChangelogFile="true" />
    <include file="parts/0099_add_accrual_transaction_external_event_configuration.xml" relativeToChangelogFile="true" />
    <include file="parts/0100_new_repayment_strategy.xml" relativeToChangelogFile="true" />
    <include file="parts/0101_update_transaction_summary_table_report.xml" relativeToChangelogFile="true" />
    <include file="parts/0102_add_external_event_for_loan_reschedule.xml" relativeToChangelogFile="true" />
    <include file="parts/0103_modify_parameter_json_column_custom_job_parameters.xml" relativeToChangelogFile="true" />
    <include file="parts/0104_loan_product_add_repayment_overdue_days_config.xml" relativeToChangelogFile="true" />
    <include file="parts/0105_add_indices_loan_table.xml" relativeToChangelogFile="true" />
    <include file="parts/0106_new_repayment_strategy.xml" relativeToChangelogFile="true" />
    <include file="parts/0107_add_configuration_charges_accrual_date.xml" relativeToChangelogFile="true" />
    <include file="parts/0108_precondition_check_cob_loan_account_lock.xml" relativeToChangelogFile="true" />
    <include file="parts/0109_spring_batch_5_upgrade.xml" relativeToChangelogFile="true" />
    <include file="parts/0110_trial_balance_summary_with_asset_owner.xml" relativeToChangelogFile="true" />
    <include file="parts/0111_transaction_summary_with_asset_owner.xml" relativeToChangelogFile="true" />
    <include file="parts/0112_transaction_summary_with_asset_owner_change_report_sql.xml" relativeToChangelogFile="true" />
    <include file="parts/0113_transaction_summary_with_asset_owner_report_sql_fix.xml" relativeToChangelogFile="true" />
    <include file="parts/0114_create_cob_indices.xml" relativeToChangelogFile="true" />
    <include file="parts/0115_create_index_from_loan_transaction_id.xml" relativeToChangelogFile="true" />
    <include file="parts/0116_add_configuration_asset_externalization_of_non_active_loans.xml" relativeToChangelogFile="true" />
    <include file="parts/0117_set_datetime_precision.xml" relativeToChangelogFile="true" />
    <include file="parts/0118_add_submitted_on_date_to_savings_transaction.xml" relativeToChangelogFile="true" />
    <include file="parts/0119_add_loan_product_down_payment_configuration.xml" relativeToChangelogFile="true" />
    <include file="parts/0120_transaction_summary_with_asset_owner_report_typo_fix.xml" relativeToChangelogFile="true" />
    <include file="parts/0121_transaction_summary_with_asset_owner_report_typo_fix_2.xml" relativeToChangelogFile="true" />
    <include file="parts/0122_add_batch_job_execution_params_index.xml" relativeToChangelogFile="true" />
    <include file="parts/0123_add_is_down_payment_to_repayment_schedule.xml" relativeToChangelogFile="true" />
    <include file="parts/0124_transaction_summary_with_asset_owner_report_typo_fix_3.xml" relativeToChangelogFile="true" />
    <include file="parts/0126_client_referral.xml" relativeToChangelogFile="true" />
    <include file="parts/0127_monthly_import.xml" relativeToChangelogFile="true" />
    <include file="parts/0128_share_account_transaction.xml" relativeToChangelogFile="true" />
    <include file="parts/0125_transaction_summary_with_asset_owner_report_chargeoff_reason.xml" relativeToChangelogFile="true" />
    <include file="parts/0126_add_loan_product_installment_level_delinquency.xml" relativeToChangelogFile="true" />
    <include file="parts/0127_client_name_length.xml" relativeToChangelogFile="true" />
    <include file="parts/0128_savings_audit.xml" relativeToChangelogFile="true" />
    <include file="parts/0129_transaction_summary_with_asset_owner_report_overpaid_amount.xml" relativeToChangelogFile="true" />
    <include file="parts/0130_add_create_delinquency_action_permission.xml" relativeToChangelogFile="true" />
    <include file="parts/0131_add_client_referral_fields.xml" relativeToChangelogFile="true" />
</databaseChangeLog>
