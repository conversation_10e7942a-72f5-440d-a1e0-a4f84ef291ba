<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements. See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership. The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied. See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">
    <changeSet author="fineract" id="1" context="mysql">
        <createTable tableName="m_loan_transaction_relation">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="from_loan_transaction_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="to_loan_transaction_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="relation_type_enum" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="created_on_utc" type="DATETIME">
                <constraints nullable="false" />
            </column>
            <column name="version" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="last_modified_on_utc" type="DATETIME">
                <constraints nullable="false" />
            </column>
        </createTable>
    </changeSet>
    <changeSet author="fineract" id="1" context="postgresql">
        <createTable tableName="m_loan_transaction_relation">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="from_loan_transaction_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="to_loan_transaction_id" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="relation_type_enum" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="created_on_utc" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false" />
            </column>
            <column name="version" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="BIGINT">
                <constraints nullable="false" />
            </column>
            <column name="last_modified_on_utc" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="false" />
            </column>
        </createTable>
    </changeSet>
    <changeSet author="fineract" id="2">
        <addForeignKeyConstraint baseColumnNames="from_loan_transaction_id" baseTableName="m_loan_transaction_relation"
                                 constraintName="FK_m_loan_transaction_from_relation" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="m_loan_transaction" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="to_loan_transaction_id" baseTableName="m_loan_transaction_relation"
                                 constraintName="FK_m_loan_transaction_to_relation" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="m_loan_transaction" validate="true"/>
    </changeSet>
    <changeSet author="fineract" id="3">
        <insert tableName="m_permission">
            <column name="grouping" value="transaction_loan"/>
            <column name="code" value="CHARGEBACK_LOAN"/>
            <column name="entity_name" value="LOAN"/>
            <column name="action_name" value="CHARGEBACK"/>
            <column name="can_maker_checker" valueBoolean="false"/>
        </insert>
    </changeSet>
    <changeSet author="fineract" id="4">
        <addColumn tableName="m_payment_type">
            <column name="code_name" type="VARCHAR(100)"/>
        </addColumn>
        <addColumn tableName="m_payment_type">
            <column name="is_system_defined" type="boolean" defaultValueComputed="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="fineract" id="5">
        <insert tableName="m_payment_type">
            <column name="value" value="Repayment Adjustment Chargeback"/>
            <column name="description" value="Repayment Adjustment Chargeback"/>
            <column name="is_cash_payment" valueBoolean="false"/>
            <column name="order_position" valueNumeric="1"/>
            <column name="code_name" value="REPAYMENT_ADJUSTMENT_CHARGEBACK"/>
            <column name="is_system_defined" valueNumeric="true"/>
        </insert>
        <insert tableName="m_payment_type">
            <column name="value" value="Repayment Adjustment Refund"/>
            <column name="description" value="Repayment Adjustment Refund"/>
            <column name="is_cash_payment" valueBoolean="false"/>
            <column name="order_position" valueNumeric="1"/>
            <column name="code_name" value="REPAYMENT_ADJUSTMENT_REFUND"/>
            <column name="is_system_defined" valueBoolean="true"/>
        </insert>
    </changeSet>
</databaseChangeLog>
