<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements. See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership. The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied. See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <!--  add audit fields -->
    <changeSet id="1" author="fineract">
        <addColumn tableName="m_savings_account">
            <column name="created_by" type="BIGINT"  valueComputed="submittedon_userid"/>
            <column name="last_modified_by" type="BIGINT"/>
        </addColumn>
        <renameColumn tableName="m_savings_account_transaction" oldColumnName="appuser_id" newColumnName="created_by" columnDataType="BIGINT"/>
        <addColumn tableName="m_savings_account_transaction">
            <column name="last_modified_by" type="BIGINT"/>
        </addColumn>
        <addColumn tableName="m_deposit_account_on_hold_transaction">
            <column name="created_by" type="BIGINT"/>
            <column name="last_modified_by" type="BIGINT"/>
        </addColumn>
        <addColumn tableName="m_savings_account_charge">
            <column name="created_by" type="BIGINT"/>
            <column name="last_modified_by" type="BIGINT"/>
        </addColumn>
        <renameColumn tableName="m_savings_officer_assignment_history" oldColumnName="createdby_id" newColumnName="created_by" columnDataType="BIGINT"/>
        <renameColumn tableName="m_savings_officer_assignment_history" oldColumnName="lastmodifiedby_id" newColumnName="last_modified_by" columnDataType="BIGINT"/>
        <renameColumn tableName="m_mandatory_savings_schedule" oldColumnName="createdby_id" newColumnName="created_by" columnDataType="BIGINT"/>
        <renameColumn tableName="m_mandatory_savings_schedule" oldColumnName="lastmodifiedby_id" newColumnName="last_modified_by" columnDataType="BIGINT"/>
    </changeSet>
    <changeSet id="2" author="fineract" context="mysql">
        <addColumn tableName="m_savings_account">
            <column name="created_on_utc" type="DATETIME(6)"/>
            <column name="last_modified_on_utc" type="DATETIME(6)"/>
        </addColumn>
        <addColumn tableName="m_savings_account_transaction">
            <column name="created_on_utc" type="DATETIME(6)"/>
            <column name="last_modified_on_utc" type="DATETIME(6)"/>
        </addColumn>
        <addColumn tableName="m_deposit_account_on_hold_transaction">
            <column name="created_on_utc" type="DATETIME(6)"/>
            <column name="last_modified_on_utc" type="DATETIME(6)"/>
        </addColumn>
        <addColumn tableName="m_savings_account_charge">
            <column name="created_on_utc" type="DATETIME(6)"/>
            <column name="last_modified_on_utc" type="DATETIME(6)"/>
        </addColumn>
        <addColumn tableName="m_savings_officer_assignment_history">
            <column name="created_on_utc" type="DATETIME(6)"/>
            <column name="last_modified_on_utc" type="DATETIME(6)"/>
        </addColumn>
        <addColumn tableName="m_mandatory_savings_schedule">
            <column name="created_on_utc" type="DATETIME(6)"/>
            <column name="last_modified_on_utc" type="DATETIME(6)"/>
        </addColumn>
    </changeSet>
    <changeSet id="2" author="fineract" context="postgresql">
        <addColumn tableName="m_savings_account">
            <column name="created_on_utc" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="last_modified_on_utc" type="TIMESTAMP WITH TIME ZONE"/>
        </addColumn>
        <addColumn tableName="m_savings_account_transaction">
            <column name="created_on_utc" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="last_modified_on_utc" type="TIMESTAMP WITH TIME ZONE"/>
        </addColumn>
        <addColumn tableName="m_deposit_account_on_hold_transaction">
            <column name="created_on_utc" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="last_modified_on_utc" type="TIMESTAMP WITH TIME ZONE"/>
        </addColumn>
        <addColumn tableName="m_savings_account_charge">
            <column name="created_on_utc" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="last_modified_on_utc" type="TIMESTAMP WITH TIME ZONE"/>
        </addColumn>
        <addColumn tableName="m_savings_officer_assignment_history">
            <column name="created_on_utc" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="last_modified_on_utc" type="TIMESTAMP WITH TIME ZONE"/>
        </addColumn>
        <addColumn tableName="m_mandatory_savings_schedule">
            <column name="created_on_utc" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="last_modified_on_utc" type="TIMESTAMP WITH TIME ZONE"/>
        </addColumn>
    </changeSet>

    <!--  add audit constraints -->
    <changeSet id="3" author="fineract" context="mysql">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(*) from m_savings_account</sqlCheck>
        </preConditions>
        <addNotNullConstraint tableName="m_savings_account" columnName="created_by" columnDataType="BIGINT"/>
        <addNotNullConstraint tableName="m_savings_account" columnName="last_modified_by" columnDataType="BIGINT"/>
        <addNotNullConstraint tableName="m_savings_account" columnName="created_on_utc" columnDataType="DATETIME(6)"/>
        <addNotNullConstraint tableName="m_savings_account" columnName="last_modified_on_utc" columnDataType="DATETIME(6)"/>
    </changeSet>
    <changeSet id="3" author="fineract" context="postgresql">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(*) from m_savings_account</sqlCheck>
        </preConditions>
        <addNotNullConstraint tableName="m_savings_account" columnName="created_by"/>
        <addNotNullConstraint tableName="m_savings_account" columnName="last_modified_by"/>
        <addNotNullConstraint tableName="m_savings_account" columnName="created_on_utc"/>
        <addNotNullConstraint tableName="m_savings_account" columnName="last_modified_on_utc"/>
    </changeSet>
    <changeSet id="4" author="fineract" context="mysql">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(*) from m_savings_account_transaction</sqlCheck>
        </preConditions>
        <addNotNullConstraint tableName="m_savings_account_transaction" columnName="created_by" columnDataType="BIGINT"/>
        <addNotNullConstraint tableName="m_savings_account_transaction" columnName="last_modified_by" columnDataType="BIGINT"/>
        <addNotNullConstraint tableName="m_savings_account_transaction" columnName="created_on_utc" columnDataType="DATETIME(6)"/>
        <addNotNullConstraint tableName="m_savings_account_transaction" columnName="last_modified_on_utc" columnDataType="DATETIME(6)"/>
    </changeSet>
    <changeSet id="4" author="fineract" context="postgresql">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(*) from m_savings_account_transaction</sqlCheck>
        </preConditions>
        <addNotNullConstraint tableName="m_savings_account_transaction" columnName="created_by"/>
        <addNotNullConstraint tableName="m_savings_account_transaction" columnName="last_modified_by"/>
        <addNotNullConstraint tableName="m_savings_account_transaction" columnName="created_on_utc"/>
        <addNotNullConstraint tableName="m_savings_account_transaction" columnName="last_modified_on_utc"/>
    </changeSet>
    <changeSet id="5" author="fineract" context="mysql">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(*) from m_deposit_account_on_hold_transaction</sqlCheck>
        </preConditions>
        <addNotNullConstraint tableName="m_deposit_account_on_hold_transaction" columnName="created_by" columnDataType="BIGINT"/>
        <addNotNullConstraint tableName="m_deposit_account_on_hold_transaction" columnName="last_modified_by" columnDataType="BIGINT"/>
        <addNotNullConstraint tableName="m_deposit_account_on_hold_transaction" columnName="created_on_utc" columnDataType="DATETIME(6)"/>
        <addNotNullConstraint tableName="m_deposit_account_on_hold_transaction" columnName="last_modified_on_utc" columnDataType="DATETIME(6)"/>
    </changeSet>
    <changeSet id="5" author="fineract" context="postgresql">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(*) from m_deposit_account_on_hold_transaction</sqlCheck>
        </preConditions>
        <addNotNullConstraint tableName="m_deposit_account_on_hold_transaction" columnName="created_by"/>
        <addNotNullConstraint tableName="m_deposit_account_on_hold_transaction" columnName="last_modified_by"/>
        <addNotNullConstraint tableName="m_deposit_account_on_hold_transaction" columnName="created_on_utc"/>
        <addNotNullConstraint tableName="m_deposit_account_on_hold_transaction" columnName="last_modified_on_utc"/>
    </changeSet>
    <changeSet id="6" author="fineract" context="mysql">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(*) from m_savings_account_charge</sqlCheck>
        </preConditions>
        <addNotNullConstraint tableName="m_savings_account_charge" columnName="created_by" columnDataType="BIGINT"/>
        <addNotNullConstraint tableName="m_savings_account_charge" columnName="last_modified_by" columnDataType="BIGINT"/>
        <addNotNullConstraint tableName="m_savings_account_charge" columnName="created_on_utc" columnDataType="DATETIME(6)"/>
        <addNotNullConstraint tableName="m_savings_account_charge" columnName="last_modified_on_utc" columnDataType="DATETIME(6)"/>
    </changeSet>
    <changeSet id="6" author="fineract" context="postgresql">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(*) from m_savings_account_charge</sqlCheck>
        </preConditions>
        <addNotNullConstraint tableName="m_savings_account_charge" columnName="created_by"/>
        <addNotNullConstraint tableName="m_savings_account_charge" columnName="last_modified_by"/>
        <addNotNullConstraint tableName="m_savings_account_charge" columnName="created_on_utc"/>
        <addNotNullConstraint tableName="m_savings_account_charge" columnName="last_modified_on_utc"/>
    </changeSet>
    <changeSet id="7" author="fineract" context="mysql">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(*) from m_savings_officer_assignment_history</sqlCheck>
        </preConditions>
        <addNotNullConstraint tableName="m_savings_officer_assignment_history" columnName="created_by" columnDataType="BIGINT"/>
        <addNotNullConstraint tableName="m_savings_officer_assignment_history" columnName="last_modified_by" columnDataType="BIGINT"/>
        <addNotNullConstraint tableName="m_savings_officer_assignment_history" columnName="created_on_utc" columnDataType="DATETIME(6)"/>
        <addNotNullConstraint tableName="m_savings_officer_assignment_history" columnName="last_modified_on_utc" columnDataType="DATETIME(6)"/>
    </changeSet>
    <changeSet id="7" author="fineract" context="postgresql">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(*) from m_savings_officer_assignment_history</sqlCheck>
        </preConditions>
        <addNotNullConstraint tableName="m_savings_officer_assignment_history" columnName="created_by"/>
        <addNotNullConstraint tableName="m_savings_officer_assignment_history" columnName="last_modified_by"/>
        <addNotNullConstraint tableName="m_savings_officer_assignment_history" columnName="created_on_utc"/>
        <addNotNullConstraint tableName="m_savings_officer_assignment_history" columnName="last_modified_on_utc"/>
    </changeSet>
    <changeSet id="8" author="fineract" context="mysql">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(*) from m_mandatory_savings_schedule</sqlCheck>
        </preConditions>
        <addNotNullConstraint tableName="m_mandatory_savings_schedule" columnName="created_by" columnDataType="BIGINT"/>
        <addNotNullConstraint tableName="m_mandatory_savings_schedule" columnName="last_modified_by" columnDataType="BIGINT"/>
        <addNotNullConstraint tableName="m_mandatory_savings_schedule" columnName="created_on_utc" columnDataType="DATETIME(6)"/>
        <addNotNullConstraint tableName="m_mandatory_savings_schedule" columnName="last_modified_on_utc" columnDataType="DATETIME(6)"/>
    </changeSet>
    <changeSet id="8" author="fineract" context="postgresql">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">select count(*) from m_mandatory_savings_schedule</sqlCheck>
        </preConditions>
        <addNotNullConstraint tableName="m_mandatory_savings_schedule" columnName="created_by"/>
        <addNotNullConstraint tableName="m_mandatory_savings_schedule" columnName="last_modified_by"/>
        <addNotNullConstraint tableName="m_mandatory_savings_schedule" columnName="created_on_utc"/>
        <addNotNullConstraint tableName="m_mandatory_savings_schedule" columnName="last_modified_on_utc"/>
    </changeSet>
    <changeSet id="9" author="fineract">
        <addForeignKeyConstraint baseColumnNames="created_by" baseTableName="m_savings_account"
                                 constraintName="FK_savings_account_created_by" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="m_appuser" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="last_modified_by" baseTableName="m_savings_account"
                                 constraintName="FK_savings_account_last_modified_by" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="m_appuser" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="created_by" baseTableName="m_savings_account_transaction"
                                 constraintName="FK_savings_account_transaction_created_by" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="m_appuser" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="last_modified_by" baseTableName="m_savings_account_transaction"
                                 constraintName="FK_savings_account_transaction_last_modified_by" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="m_appuser" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="created_by" baseTableName="m_deposit_account_on_hold_transaction"
                                 constraintName="FK_deposit_account_on_hold_transaction_created_by" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="m_appuser" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="last_modified_by" baseTableName="m_deposit_account_on_hold_transaction"
                                 constraintName="FK_deposit_account_on_hold_transaction_last_modified_by" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="m_appuser" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="created_by" baseTableName="m_savings_account_charge"
                                 constraintName="FK_savings_account_charge_created_by" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="m_appuser" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="last_modified_by" baseTableName="m_savings_account_charge"
                                 constraintName="FK_savings_account_charge_last_modified_by" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="m_appuser" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="created_by" baseTableName="m_savings_officer_assignment_history"
                                 constraintName="FK_savings_officer_assignment_history_created_by" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="m_appuser" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="last_modified_by" baseTableName="m_savings_officer_assignment_history"
                                 constraintName="FK_savings_officer_assignment_history_last_modified_by" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="m_appuser" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="created_by" baseTableName="m_mandatory_savings_schedule"
                                 constraintName="FK_mandatory_savings_schedule_created_by" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="m_appuser" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="last_modified_by" baseTableName="m_mandatory_savings_schedule"
                                 constraintName="FK_mandatory_savings_schedule_last_modified_by" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="m_appuser" validate="true"/>
    </changeSet>

    <!--  old fields -->
    <changeSet id="10" author="fineract">
        <dropNotNullConstraint tableName="m_savings_account_transaction" columnName="created_date" columnDataType="DATETIME"/>
        <dropNotNullConstraint tableName="m_deposit_account_on_hold_transaction" columnName="created_date" columnDataType="DATETIME"/>
    </changeSet>
</databaseChangeLog>
