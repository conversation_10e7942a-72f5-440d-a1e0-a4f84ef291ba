<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements. See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership. The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied. See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="fineract" id="1-postgresql" context="postgresql">
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1"/>
            <column name="parameter_name" value="startDateSelect"/>
            <column name="parameter_variable" value="startDate"/>
            <column name="parameter_label" value="startDate"/>
            <column name="parameter_displayType" value="date"/>
            <column name="parameter_FormatType" value="date"/>
            <column name="parameter_default" value="today"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="2"/>
            <column name="parameter_name" value="endDateSelect"/>
            <column name="parameter_variable" value="endDate"/>
            <column name="parameter_label" value="endDate"/>
            <column name="parameter_displayType" value="date"/>
            <column name="parameter_FormatType" value="date"/>
            <column name="parameter_default" value="today"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="3"/>
            <column name="parameter_name" value="obligDateTypeSelect"/>
            <column name="parameter_variable" value="obligDateType"/>
            <column name="parameter_label" value="obligDateType"/>
            <column name="parameter_displayType" value="select"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="0"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql" value="select * from  (select 1 as id, 'Closed' AS name union all select 2, 'Disbursal' ) x  order by x.id"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="5"/>
            <column name="parameter_name" value="OfficeIdSelectOne"/>
            <column name="parameter_variable" value="officeId"/>
            <column name="parameter_label" value="Office"/>
            <column name="parameter_displayType" value="select"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="0"/>
            <column name="special"/>
            <column name="selectOne" value="Y"/>
            <column name="selectAll"/>
            <column name="parameter_sql" value="select id, concat(substring('........................................', 1,        ((LENGTH(hierarchy) - LENGTH(REPLACE(hierarchy, '.', '')) - 1) * 4)),      name) as tc  from m_office  where hierarchy like concat('${currentUserHierarchy}', '%')  order by hierarchy"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="6"/>
            <column name="parameter_name" value="loanOfficerIdSelectAll"/>
            <column name="parameter_variable" value="loanOfficerId"/>
            <column name="parameter_label" value="Loan Officer"/>
            <column name="parameter_displayType" value="select"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="0"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll" value="Y"/>
            <column name="parameter_sql" value="(select lo.id, lo.display_name AS name   from m_office o join m_office ounder on ounder.hierarchy like concat(o.hierarchy, '%')  join m_staff lo on lo.office_id = ounder.id  where lo.is_loan_officer = true  and o.id = '${officeId}')  union all  (select -10, '-')  order by 2"/>
            <column name="parent_id" valueNumeric="5"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="10"/>
            <column name="parameter_name" value="currencyIdSelectAll"/>
            <column name="parameter_variable" value="currencyId"/>
            <column name="parameter_label" value="Currency"/>
            <column name="parameter_displayType" value="select"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="0"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll" value="Y"/>
            <column name="parameter_sql" value="select code, name  from m_organisation_currency  order by code"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="20"/>
            <column name="parameter_name" value="fundIdSelectAll"/>
            <column name="parameter_variable" value="fundId"/>
            <column name="parameter_label" value="Fund"/>
            <column name="parameter_displayType" value="select"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="0"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll" value="Y"/>
            <column name="parameter_sql" value="(select id, name  from m_fund)  union all  (select -10, '-')  order by 2"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="25"/>
            <column name="parameter_name" value="loanProductIdSelectAll"/>
            <column name="parameter_variable" value="loanProductId"/>
            <column name="parameter_label" value="Product"/>
            <column name="parameter_displayType" value="select"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="0"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll" value="Y"/>
            <column name="parameter_sql"
                    value="select p.id, p.`name`&#13;&#10;from m_product_loan p&#13;&#10;where (p.currency_code = '${currencyId}' or '-1'= '${currencyId}')&#13;&#10;order by 2"/>
            <column name="parent_id" valueNumeric="10"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="26"/>
            <column name="parameter_name" value="loanPurposeIdSelectAll"/>
            <column name="parameter_variable" value="loanPurposeId"/>
            <column name="parameter_label" value="Loan Purpose"/>
            <column name="parameter_displayType" value="select"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="0"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll" value="Y"/>
            <column name="parameter_sql" value="select -10 as id, '-' as code_value  union all  select * from (select v.id, v.code_value  from m_code c  join m_code_value v on v.code_id = c.id  where c.code_name = 'loanPurpose'  order by v.order_position)  x"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="100"/>
            <column name="parameter_name" value="parTypeSelect"/>
            <column name="parameter_variable" value="parType"/>
            <column name="parameter_label" value="parType"/>
            <column name="parameter_displayType" value="select"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="0"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql" value="select * from  (select 1 as id, 'Principal Only' AS name union all  select 2, 'Principal + Interest' union all select 3, 'Principal + Interest + Fees' union all  select 4, 'Principal + Interest + Fees + Penalties') x  order by x.id"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1001"/>
            <column name="parameter_name" value="FullReportList"/>
            <column name="parameter_variable"/>
            <column name="parameter_label" value="n/a"/>
            <column name="parameter_displayType" value="n/a"/>
            <column name="parameter_FormatType" value="n/a"/>
            <column name="parameter_default" value="n/a"/>
            <column name="special" value="Y"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql" value="SELECT r.id  AS report_id, r.report_name, r.report_type, r.report_subtype, r.report_category, rp.id AS parameter_id, rp.report_parameter_name, p.parameter_name FROM   stretchy_report r LEFT JOIN stretchy_report_parameter rp ON rp.report_id = r.id LEFT JOIN stretchy_parameter p ON p.id = rp.parameter_id WHERE  r.use_report IS TRUE AND r.self_service_user_report = false AND EXISTS (SELECT 'f' FROM   m_appuser_role ur JOIN m_role rr ON rr.id = ur.role_id JOIN m_role_permission rp ON rp.role_id = rr.id JOIN m_permission p ON p.id = rp.permission_id WHERE  ur.appuser_id = 1 AND ( p.code IN ( 'ALL_FUNCTIONS_READ', 'ALL_FUNCTIONS' ) OR p.code = Concat('READ_', r.report_name) )) ORDER  BY r.report_category, r.report_name, rp.id"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1002"/>
            <column name="parameter_name" value="FullParameterList"/>
            <column name="parameter_variable"/>
            <column name="parameter_label" value="n/a"/>
            <column name="parameter_displayType" value="n/a"/>
            <column name="parameter_FormatType" value="n/a"/>
            <column name="parameter_default" value="n/a"/>
            <column name="special" value="Y"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql" value="select sp.parameter_name, sp.parameter_variable, sp.parameter_label, sp.&quot;parameter_display_type&quot;, sp.&quot;parameter_format_type&quot;, sp.parameter_default, sp.&quot;selectOne&quot;,  sp.&quot;selectAll&quot;, spp.parameter_name as parentParameterName from stretchy_parameter sp  left join stretchy_parameter spp on spp.id = sp.parent_id  where sp.special is null  and exists     (select 'f'    from stretchy_report sr    join stretchy_report_parameter srp on srp.report_id = sr.id   and sr.self_service_user_report = ${isSelfServiceUser}    where sr.report_name in(${reportListing})    and srp.parameter_id = sp.id   ) order by sp.id"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1003"/>
            <column name="parameter_name" value="reportCategoryList"/>
            <column name="parameter_variable"/>
            <column name="parameter_label" value="n/a"/>
            <column name="parameter_displayType" value="n/a"/>
            <column name="parameter_FormatType" value="n/a"/>
            <column name="parameter_default" value="n/a"/>
            <column name="special" value="Y"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql" value="select  r.id as report_id, r.report_name, r.report_type, r.report_subtype, r.report_category,   rp.id as parameter_id, rp.report_parameter_name, p.parameter_name from stretchy_report r   left join stretchy_report_parameter rp on rp.report_id = r.id   left join stretchy_parameter p on p.id = rp.parameter_id   where r.report_category = '${reportCategory}'   and r.use_report is true and r.self_service_user_report = '${isSelfServiceUser}'     and exists   (select 'f'   from m_appuser_role ur    join m_role rr on rr.id = ur.role_id   join m_role_permission rp on rp.role_id = rr.id   join m_permission p on p.id = rp.permission_id   where ur.appuser_id = ${currentUserId} and (p.code in ('ALL_FUNCTIONS_READ', 'ALL_FUNCTIONS') or p.code = concat('READ_', r.report_name)) )   order by r.report_category, r.report_name, rp.id"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1004"/>
            <column name="parameter_name" value="selectAccount"/>
            <column name="parameter_variable" value="accountNo"/>
            <column name="parameter_label" value="Enter Account No"/>
            <column name="parameter_displayType" value="text"/>
            <column name="parameter_FormatType" value="string"/>
            <column name="parameter_default" value="n/a"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1005"/>
            <column name="parameter_name" value="savingsProductIdSelectAll"/>
            <column name="parameter_variable" value="savingsProductId"/>
            <column name="parameter_label" value="Product"/>
            <column name="parameter_displayType" value="select"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="0"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll" value="Y"/>
            <column name="parameter_sql" value="select p.id, p.name  from m_savings_product p  order by 2"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1006"/>
            <column name="parameter_name" value="transactionId"/>
            <column name="parameter_variable" value="transactionId"/>
            <column name="parameter_label" value="transactionId"/>
            <column name="parameter_displayType" value="text"/>
            <column name="parameter_FormatType" value="string"/>
            <column name="parameter_default" value="n/a"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1007"/>
            <column name="parameter_name" value="selectCenterId"/>
            <column name="parameter_variable" value="centerId"/>
            <column name="parameter_label" value="Enter Center Id"/>
            <column name="parameter_displayType" value="text"/>
            <column name="parameter_FormatType" value="string"/>
            <column name="parameter_default" value="n/a"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1008"/>
            <column name="parameter_name" value="SelectGLAccountNO"/>
            <column name="parameter_variable" value="GLAccountNO"/>
            <column name="parameter_label" value="GLAccountNO"/>
            <column name="parameter_displayType" value="select"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="0"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql" value="select id aid,name aname  from acc_gl_account"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1009"/>
            <column name="parameter_name" value="asOnDate"/>
            <column name="parameter_variable" value="asOn"/>
            <column name="parameter_label" value="As On"/>
            <column name="parameter_displayType" value="date"/>
            <column name="parameter_FormatType" value="date"/>
            <column name="parameter_default" value="today"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1010"/>
            <column name="parameter_name" value="SavingsAccountSubStatus"/>
            <column name="parameter_variable" value="subStatus"/>
            <column name="parameter_label" value="SavingsAccountDormancyStatus"/>
            <column name="parameter_displayType" value="select"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="100"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql" value="select * from  (select 100 as id, 'Inactive' as name  union all  select 200 as id, 'Dormant' as  name union all   select 300 as id, 'Escheat' as name) x  order by x.id"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1011"/>
            <column name="parameter_name" value="cycleXSelect"/>
            <column name="parameter_variable" value="cycleX"/>
            <column name="parameter_label" value="Cycle X Number"/>
            <column name="parameter_displayType" value="text"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="n/a"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1012"/>
            <column name="parameter_name" value="cycleYSelect"/>
            <column name="parameter_variable" value="cycleY"/>
            <column name="parameter_label" value="Cycle Y Number"/>
            <column name="parameter_displayType" value="text"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="n/a"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1013"/>
            <column name="parameter_name" value="fromXSelect"/>
            <column name="parameter_variable" value="fromX"/>
            <column name="parameter_label" value="From X Number"/>
            <column name="parameter_displayType" value="text"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="n/a"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1014"/>
            <column name="parameter_name" value="toYSelect"/>
            <column name="parameter_variable" value="toY"/>
            <column name="parameter_label" value="To Y Number"/>
            <column name="parameter_displayType" value="text"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="n/a"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1015"/>
            <column name="parameter_name" value="overdueXSelect"/>
            <column name="parameter_variable" value="overdueX"/>
            <column name="parameter_label" value="Overdue X Number"/>
            <column name="parameter_displayType" value="text"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="n/a"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1016"/>
            <column name="parameter_name" value="overdueYSelect"/>
            <column name="parameter_variable" value="overdueY"/>
            <column name="parameter_label" value="Overdue Y Number"/>
            <column name="parameter_displayType" value="text"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="n/a"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll"/>
            <column name="parameter_sql"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1017"/>
            <column name="parameter_name" value="DefaultLoan"/>
            <column name="parameter_variable" value="loanId"/>
            <column name="parameter_label" value="Loan"/>
            <column name="parameter_displayType" value="none"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="-1"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll" value="Y"/>
            <column name="parameter_sql" value="select ml.id  from m_loan ml  left join m_client mc on mc.id = ml.client_id  left join m_office mo on mo.id = mc.office_id  where mo.id = '${officeId}' or '${officeId}' = -1"/>
            <column name="parent_id" valueNumeric="5"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1018"/>
            <column name="parameter_name" value="DefaultClient"/>
            <column name="parameter_variable" value="clientId"/>
            <column name="parameter_label" value="Client"/>
            <column name="parameter_displayType" value="none"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="-1"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll" value="Y"/>
            <column name="parameter_sql" value="select mc.id  from m_client mc  left join m_office mo on mc.office_id = mo.id  where mo.id = '${officeId}' or '${officeId}' = -1"/>
            <column name="parent_id" valueNumeric="5"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1019"/>
            <column name="parameter_name" value="DefaultGroup"/>
            <column name="parameter_variable" value="groupId"/>
            <column name="parameter_label" value="Group"/>
            <column name="parameter_displayType" value="none"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="-1"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll" value="Y"/>
            <column name="parameter_sql" value="select mg.id  from m_group mg left join m_office mo on mg.office_id = mo.id where mo.id = '${officeId}' or '${officeId}' = -1"/>
            <column name="parent_id" valueNumeric="5"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1020"/>
            <column name="parameter_name" value="SelectLoanType"/>
            <column name="parameter_variable" value="loanType"/>
            <column name="parameter_label" value="Loan Type"/>
            <column name="parameter_displayType" value="select"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="-1"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll" value="Y"/>
            <column name="parameter_sql" value="select enum_id as id, enum_value as value from r_enum_value where enum_name = 'loan_type_enum'"/>
            <column name="parent_id"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1021"/>
            <column name="parameter_name" value="DefaultSavings"/>
            <column name="parameter_variable" value="savingsId"/>
            <column name="parameter_label" value="Savings"/>
            <column name="parameter_displayType" value="none"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="-1"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll" value="Y"/>
            <column name="parameter_sql"/>
            <column name="parent_id" valueNumeric="5"/>
        </insert>
        <insert tableName="stretchy_parameter">
            <column name="id" valueNumeric="1022"/>
            <column name="parameter_name" value="DefaultSavingsTransactionId"/>
            <column name="parameter_variable" value="savingsTransactionId"/>
            <column name="parameter_label" value="Savings Transaction"/>
            <column name="parameter_displayType" value="none"/>
            <column name="parameter_FormatType" value="number"/>
            <column name="parameter_default" value="-1"/>
            <column name="special"/>
            <column name="selectOne"/>
            <column name="selectAll" value="Y"/>
            <column name="parameter_sql"/>
            <column name="parent_id" valueNumeric="5"/>
        </insert>
    </changeSet>
    <changeSet author="fineract" id="2-postgresql" context="postgresql">
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="1"/>
            <column name="report_name" value="Client Listing"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Client"/>
            <column name="report_sql" value="SELECT Concat(REPEAT('..', (( Length(ounder.hierarchy) - Length( REPLACE(ounder.hierarchy, '.', '')) - 1 ))) , ounder.name)  AS &quot;Office/Branch&quot;, c.account_no  AS &quot;Client Account No.&quot;, c.display_name  AS name, r.enum_message_property AS &quot;Status&quot;, c.activation_date AS &quot;Activation&quot;, c.external_id AS &quot;External Id&quot; FROM   m_office o JOIN m_office ounder ON ounder.hierarchy LIKE Concat(o.hierarchy, '%') AND ounder.hierarchy LIKE Concat('${currentUserHierarchy}', '%') JOIN m_client c ON c.office_id = ounder.id LEFT JOIN r_enum_value r ON r.enum_name = 'status_enum' AND r.enum_id = c.status_enum WHERE  o.id = '${officeId}' ORDER  BY ounder.hierarchy, c.account_no"/>
            <column name="description" value="Individual Client Report    Lists the small number of defined fields on the client table.  Would expect to copy this   report and add any 'one to one' additional data for specific tenant needs.    Can be run for any size MFI but you'd expect it only to be run within a branch for   larger ones.  Depending on how many columns are displayed, there is probably is a limit of about 20/50k clients returned for html display (export to excel doesn't   have that client browser/memory impact)."/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="2"/>
            <column name="report_name" value="Client Loans Listing"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Client"/>
            <column name="report_sql" value="SELECT    Concat(REPEAT('..', ((Length(ounder.hierarchy) - Length(REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS &quot;Office/Branch&quot;, c.account_no AS &quot;Client Account No.&quot;, c.display_name AS name, r.enum_message_property  AS &quot;Client Status&quot;, lo.display_name  AS &quot;Loan Officer&quot;, l.account_no AS &quot;Loan Account No.&quot;, l.external_id  AS &quot;External Id&quot;, p.name AS loan, st.enum_message_property AS &quot;Status&quot;, f.name AS fund, purp.code_value  AS &quot;Loan Purpose&quot;, Coalesce(cur.display_symbol, l.currency_code)  AS currency, l.principal_amount, l.arrearstolerance_amount AS &quot;Arrears Tolerance Amount&quot;, l.number_of_repayments  AS &quot;Expected No. Repayments&quot;, l.annual_nominal_interest_rate  AS &quot;Annual Nominal Interest Rate&quot;, l.nominal_interest_rate_per_period  AS &quot;Nominal Interest Rate Per Period&quot;, ipf.enum_message_property AS &quot;Interest Rate Frequency&quot;, im.enum_message_property  AS &quot;Interest Method&quot;, icp.enum_message_property AS &quot;Interest Calculated in Period&quot;, l.term_frequency  AS &quot;Term Frequency&quot;, tf.enum_message_property  AS &quot;Term Frequency Period&quot;, l.repay_every AS &quot;Repayment Frequency&quot;, rf.enum_message_property  AS &quot;Repayment Frequency Period&quot;, am.enum_message_property  AS &quot;Amortization&quot;, l.total_charges_due_at_disbursement_derived AS &quot;Total Charges Due At Disbursement&quot;, DATE_TRUNC('day', l.submittedon_date) AS submitted, DATE_TRUNC('day', l.approvedon_date)                   approved, l.expected_disbursedon_date AS &quot;Expected Disbursal&quot;, DATE_TRUNC('day', l.expected_firstrepaymenton_date) AS &quot;Expected First Repayment&quot;, DATE_TRUNC('day', l.interest_calculated_from_date)  AS &quot;Interest Calculated From&quot; , DATE_TRUNC('day', l.disbursedon_date) AS disbursed, DATE_TRUNC('day', l.expected_maturedon_date)  AS &quot;Expected Maturity&quot;, DATE_TRUNC('day', l.maturedon_date) AS &quot;Matured On&quot;, DATE_TRUNC('day', l.closedon_date)  AS closed, DATE_TRUNC('day', l.rejectedon_date)  AS rejected, DATE_TRUNC('day', l.rescheduledon_date) AS rescheduled, DATE_TRUNC('day', l.withdrawnon_date) AS withdrawn, DATE_TRUNC('day', l.writtenoffon_date)  AS &quot;Written Off&quot; FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id LEFT JOIN r_enum_value r ON        r.enum_name = 'status_enum' AND       r.enum_id = c.status_enum LEFT JOIN m_loan l ON        l.client_id = c.id LEFT JOIN m_staff lo ON        lo.id = l.loan_officer_id LEFT JOIN m_product_loan p ON        p.id = l.product_id LEFT JOIN m_fund f ON        f.id = l.fund_id LEFT JOIN r_enum_value st ON        st.enum_name = 'loan_status_id' AND       st.enum_id = l.loan_status_id LEFT JOIN r_enum_value ipf ON        ipf.enum_name = 'interest_period_frequency_enum' AND       ipf.enum_id = l.interest_period_frequency_enum LEFT JOIN r_enum_value im ON        im.enum_name = 'interest_method_enum' AND       im.enum_id = l.interest_method_enum LEFT JOIN r_enum_value tf ON        tf.enum_name = 'term_period_frequency_enum' AND       tf.enum_id = l.term_period_frequency_enum LEFT JOIN r_enum_value icp ON        icp.enum_name = 'interest_calculated_in_period_enum' AND       icp.enum_id = l.interest_calculated_in_period_enum LEFT JOIN r_enum_value rf ON        rf.enum_name = 'repayment_period_frequency_enum' AND       rf.enum_id = l.repayment_period_frequency_enum LEFT JOIN r_enum_value am ON        am.enum_name = 'amortization_method_enum' AND       am.enum_id = l.amortization_method_enum LEFT JOIN m_code_value purp ON        purp.id = l.loanpurpose_cv_id LEFT JOIN m_currency cur ON        cur.code = l.currency_code WHERE     o.id = '${officeId}' AND       ( l.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') AND       ( l.product_id = '${loanProductId}' OR        '-1' = '${loanProductId}') AND       ( coalesce(l.loan_officer_id, -10) = ${loanOfficerId} OR        '-1' = ${loanOfficerId}) AND       ( coalesce(l.fund_id, -10) = ${fundId} OR        -1 = ${fundId}) AND       ( coalesce(l.loanpurpose_cv_id, -10) = ${loanPurposeId} OR        -1 = ${loanPurposeId}) ORDER BY  ounder.hierarchy, 2 , l.id"/>
            <column name="description" value="Individual Client Report    Pretty   wide report that lists the basic details of client loans.      Can be run for any size MFI but you'd expect it only to be run within a branch for larger ones.    There is probably is a limit of about 20/50k clients returned for html display (export to excel doesn't have that client browser/memory impact)."/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="5"/>
            <column name="report_name" value="Loans Awaiting Disbursal"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT    Concat(REPEAT('..', ((Length(ounder.hierarchy) - Length(REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS &quot;Office/Branch&quot;, c.account_no AS &quot;Client Account No&quot;, c.display_name AS name, l.account_no AS &quot;Loan Account No.&quot;, pl.name  AS &quot;Product&quot;, f.name AS fund, Coalesce(cur.display_symbol, l.currency_code)  AS currency, l.principal_amount AS principal, l.term_frequency AS &quot;Term Frequency&quot;, tf.enum_message_property AS &quot;Term Frequency Period&quot;, l.annual_nominal_interest_rate AS &quot;Annual Nominal Interest Rate&quot;, DATE_TRUNC('day', l.approvedon_date) AS &quot;Approved&quot;, extract(day FROM (l.expected_disbursedon_date::TIMESTAMP - CURRENT_DATE)) AS &quot;Days to Disbursal&quot;, DATE_TRUNC('day', l.expected_disbursedon_date) AS &quot;Expected Disbursal&quot;, purp.code_value AS &quot;Loan Purpose&quot;, lo.display_name AS &quot;Loan Officer&quot; FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan l ON        l.client_id = c.id JOIN      m_product_loan pl ON        pl.id = l.product_id LEFT JOIN m_staff lo ON        lo.id = l.loan_officer_id LEFT JOIN m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_fund f ON        f.id = l.fund_id LEFT JOIN m_code_value purp ON        purp.id = l.loanpurpose_cv_id LEFT JOIN r_enum_value tf ON        tf.enum_name = 'term_period_frequency_enum' AND       tf.enum_id = l.term_period_frequency_enum WHERE     o.id = '${officeId}' AND       ( l.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') AND       ( l.product_id = '${loanProductId}' OR        '-1' = '${loanProductId}') AND       ( coalesce(l.loan_officer_id, -10) = '${loanOfficerId}' OR        '-1' = '${loanOfficerId}') AND       ( coalesce(l.fund_id, -10) = '${fundId}' OR        -1 = '${fundId}') AND       ( coalesce(l.loanpurpose_cv_id, -10) = '${loanPurposeId}' OR        -1 = '${loanPurposeId}') AND       l.loan_status_id = 200 ORDER BY  ounder.hierarchy, extract(day FROM (l.expected_disbursedon_date::TIMESTAMP - CURRENT_DATE)), c.account_no"/>
            <column name="description" value="Individual Client Report"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="6"/>
            <column name="report_name" value="Loans Awaiting Disbursal Summary"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT    Concat(REPEAT('..', ((Length(ounder.hierarchy) - Length(REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS &quot;Office/Branch&quot;, pl.name  AS &quot;Product&quot;, Coalesce(cur.display_symbol, l.currency_code)  AS currency, f.name AS fund, Sum(l.principal_amount)  AS principal FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE Concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE Concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan l ON        l.client_id = c.id JOIN      m_product_loan pl ON        pl.id = l.product_id LEFT JOIN m_staff lo ON        lo.id = l.loan_officer_id LEFT JOIN m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_fund f ON        f.id = l.fund_id LEFT JOIN m_code_value purp ON        purp.id = l.loanpurpose_cv_id WHERE     o.id = '${officeId}' AND       ( l.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') AND       ( l.product_id = '${loanProductId}' OR        '-1' = '${loanProductId}') AND       ( coalesce(l.loan_officer_id, -10) = '${loanOfficerId}' OR        '-1' = '${loanOfficerId}') AND       ( coalesce(l.fund_id, -10) = '${fundId}' OR        -1 = '${fundId}') AND       ( coalesce(l.loanpurpose_cv_id, -10) = '${loanPurposeId}' OR        -1 = '${loanPurposeId}') AND       l.loan_status_id = 200 GROUP BY  ounder.hierarchy, pl.name, l.currency_code, f.name, ounder.name, cur.display_symbol ORDER BY  ounder.hierarchy, pl.name, l.currency_code, f.name"/>
            <column name="description" value="Individual Client Report"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="7"/>
            <column name="report_name" value="Loans Awaiting Disbursal Summary by Month"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT    Concat(REPEAT('..', ((Length(ounder.hierarchy) - Length(REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS &quot;Office/Branch&quot;, pl.name  AS &quot;Product&quot;, Coalesce(cur.display_symbol, l.currency_code)  AS &quot;currency&quot;, extract(year from l.expected_disbursedon_date) AS &quot;Year&quot;, to_char(l.expected_disbursedon_date, 'Month')  AS &quot;Month&quot;, Sum(l.principal_amount)  AS &quot;principal&quot; FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE Concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE Concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan l ON        l.client_id = c.id JOIN      m_product_loan pl ON        pl.id = l.product_id LEFT JOIN m_staff lo ON        lo.id = l.loan_officer_id LEFT JOIN m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_fund f ON        f.id = l.fund_id LEFT JOIN m_code_value purp ON        purp.id = l.loanpurpose_cv_id WHERE     o.id = '${officeId}' AND       ( l.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') AND       ( l.product_id = '${loanProductId}' OR        '-1' = '${loanProductId}') AND       ( Coalesce(l.loan_officer_id, -10) = '${loanOfficerId}' OR        '-1' = '${loanOfficerId}') AND       ( coalesce(l.fund_id, -10) = '${fundId}' OR        -1 = '${fundId}') AND       ( coalesce(l.loanpurpose_cv_id, -10) = '${loanPurposeId}' OR        -1 = '${loanPurposeId}') AND       l.loan_status_id = 200 GROUP BY  ounder.hierarchy, ounder.name, cur.display_symbol, pl.name, l.currency_code, l.expected_disbursedon_date ORDER BY  ounder.hierarchy, pl.name, l.currency_code, extract(year from l.expected_disbursedon_date), to_char(l.expected_disbursedon_date, 'Month')"/>
            <column name="description" value="Individual Client Report"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="8"/>
            <column name="report_name" value="Loans Pending Approval"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT    Concat(REPEAT('..', ((Length(ounder.hierarchy) - Length(REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS &quot;Office/Branch&quot;, c.account_no AS &quot;Client Account No.&quot;, c.display_name AS &quot;Client Name&quot;, Coalesce(cur.display_symbol, l.currency_code)  AS currency, pl.name  AS &quot;Product&quot;, l.account_no AS &quot;Loan Account No.&quot;, l.principal_amount AS &quot;Loan Amount&quot;, l.term_frequency AS &quot;Term Frequency&quot;, tf.enum_message_property AS &quot;Term Frequency Period&quot;, l.annual_nominal_interest_rate AS &quot; Annual Nominal Interest Rate&quot;, Extract(day FROM (CURRENT_DATE - l.submittedon_date::TIMESTAMP)) AS &quot;Days Pending Approval&quot;, purp.code_value AS &quot;Loan Purpose&quot;, lo.display_name AS &quot;Loan Officer&quot; FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan l ON        l.client_id = c.id JOIN      m_product_loan pl ON        pl.id = l.product_id LEFT JOIN m_staff lo ON        lo.id = l.loan_officer_id LEFT JOIN m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_code_value purp ON        purp.id = l.loanpurpose_cv_id LEFT JOIN r_enum_value tf ON        tf.enum_name = 'term_period_frequency_enum' AND       tf.enum_id = l.term_period_frequency_enum WHERE     o.id = '${officeId}' AND       ( l.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') AND       ( l.product_id = '${loanProductId}' OR        '-1' = '${loanProductId}') AND       ( coalesce(l.loan_officer_id, -10) = '${loanOfficerId}' OR        '-1' = '${loanOfficerId}') AND       ( coalesce(l.loanpurpose_cv_id, -10) = '${loanPurposeId}' OR        -1 = '${loanPurposeId}') AND       l.loan_status_id = 100 ORDER BY  ounder.hierarchy, l.submittedon_date, l.account_no"/>
            <column name="description" value="Individual Client Report"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="11"/>
            <column name="report_name" value="Active Loans - Summary"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT   Concat(REPEAT('..', ((Length(mo.hierarchy) - Length(REPLACE(mo.hierarchy, '.', '')) - 1))), mo.name) AS &quot;Office/Branch&quot;, x.currency AS currency, x.client_count AS &quot;No. of Clients&quot;, x.active_loan_count  AS &quot;No. Active                    Loans&quot;, x. loans_in_arrears_count  AS &quot;No. of Loans in                    Arrears&quot;, x.principal  AS &quot;Total Loans Disbursed&quot;, x.principal_repaid AS &quot;Principal Repaid&quot;, x.principal_outstanding  AS &quot;Principal Outstanding&quot;, x.principal_overdue  AS &quot;Principal Overdue&quot;, x.interest AS &quot;Total Interest&quot;, x.interest_repaid  AS &quot;Interest Repaid&quot;, x.interest_outstanding AS &quot;Interest Outstanding&quot;, x.interest_overdue AS &quot;Interest Overdue&quot;, x.fees AS &quot;Total Fees&quot;, x.fees_repaid  AS &quot;Fees Repaid&quot;, x.fees_outstanding AS &quot;Fees Outstanding&quot;, x.fees_overdue AS &quot;Fees Overdue&quot;, x.penalties  AS &quot;Total Penalties&quot;, x.penalties_repaid AS &quot;Penalties Repaid&quot;, x.penalties_outstanding  AS &quot;Penalties Outstanding&quot;, x.penalties_overdue  AS &quot;Penalties Overdue&quot;, ( CASE WHEN ${parType} = 1 THEN cast(round((x.principal_overdue * 100) / x.principal_outstanding, 2) AS                                                                                                                                     CHAR) WHEN ${parType} = 2 THEN cast(round(((x.principal_overdue + x.interest_overdue) * 100) / (x.principal_outstanding + x.interest_outstanding), 2) AS                                                                                   CHAR) WHEN ${parType} = 3 THEN cast(round(((x.principal_overdue + x.interest_overdue + x.fees_overdue) * 100) / (x.principal_outstanding + x.interest_outstanding + x.fees_outstanding), 2) AS                                             CHAR) WHEN ${parType} = 4 THEN cast(round(((x.principal_overdue + x.interest_overdue + x.fees_overdue + x.penalties_overdue) * 100) / (x.principal_outstanding + x.interest_outstanding + x.fees_outstanding + x.penalties_overdue), 2) AS CHAR) ELSE 'invalid PAR Type' end) AS &quot;Portfolio at Risk %&quot; FROM     m_office mo JOIN ( SELECT    ounder.id AS branch, coalesce(cur.display_symbol, l.currency_code) AS currency, count(DISTINCT(c.id)) AS client_count, count(DISTINCT(l.id)) AS active_loan_count, count(DISTINCT(coalesce(laa.loan_id, l.id, NULL) )) AS loans_in_arrears_count, sum(l.principal_disbursed_derived)  AS principal, sum(l.principal_repaid_derived) AS principal_repaid, sum(l.principal_outstanding_derived)  AS principal_outstanding, sum(laa.principal_overdue_derived)  AS principal_overdue, sum(l.interest_charged_derived) AS interest, sum(l.interest_repaid_derived)  AS interest_repaid, sum(l.interest_outstanding_derived) AS interest_outstanding, sum(laa.interest_overdue_derived) AS interest_overdue, sum(l.fee_charges_charged_derived)  AS fees, sum(l.fee_charges_repaid_derived) AS fees_repaid, sum(l.fee_charges_outstanding_derived)  AS fees_outstanding, sum(laa.fee_charges_overdue_derived)  AS fees_overdue, sum(l.penalty_charges_charged_derived)  AS penalties, sum(l.penalty_charges_repaid_derived) AS penalties_repaid, sum(l.penalty_charges_outstanding_derived)  AS penalties_outstanding, sum(laa.penalty_charges_overdue_derived)  AS penalties_overdue FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan l ON        l.client_id = c.id LEFT JOIN m_loan_arrears_aging laa ON        laa.loan_id = l.id LEFT JOIN m_currency cur ON        cur.code = l.currency_code WHERE     o.id = '${officeId}' AND       ( l.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') AND       ( l.product_id = '${loanProductId}' OR        '-1' = '${loanProductId}') AND       ( coalesce(l.loan_officer_id, -10) = ${loanOfficerId} OR        '-1' = ${loanOfficerId}) AND       ( coalesce(l.fund_id, -10) = ${fundId} OR        -1 = ${fundId}) AND       ( coalesce(l.loanpurpose_cv_id, -10) = ${loanPurposeId} OR        -1 = ${loanPurposeId}) AND       l.loan_status_id = 300 GROUP BY  ounder.id, l.currency_code, cur.display_symbol) x ON       x.branch = mo.id ORDER BY mo.hierarchy, x.currency"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="12"/>
            <column name="report_name" value="Active Loans - Details"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT    Concat(REPEAT('..', ((Length(ounder.hierarchy) - Length(REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS &quot;Office/Branch&quot;, Coalesce(cur.display_symbol, l.currency_code)  AS currency, lo.display_name  AS &quot;Loan Officer&quot;, c.display_name AS &quot;Client&quot;, l.account_no AS &quot;Loan Account No.&quot;, pl.name  AS &quot;Product&quot;, f.name AS fund, l.principal_amount AS &quot;Loan Amount&quot;, l.annual_nominal_interest_rate AS &quot;Annual Nominal Interest Rate&quot;, DATE_TRUNC('day', l.disbursedon_date)  AS &quot;Disbursed Date&quot;, DATE_TRUNC('day', l.expected_maturedon_date) AS &quot;Expected Matured On&quot;, l.principal_repaid_derived AS &quot;Principal Repaid&quot;, l.principal_outstanding_derived  AS &quot;Principal Outstanding&quot;, laa.principal_overdue_derived  AS &quot;Principal Overdue&quot;, l.interest_repaid_derived  AS &quot;Interest Repaid&quot;, l.interest_outstanding_derived AS &quot;Interest Outstanding&quot;, laa.interest_overdue_derived AS &quot;Interest Overdue&quot;, l.fee_charges_repaid_derived AS &quot;Fees Repaid&quot;, l.fee_charges_outstanding_derived  AS &quot;Fees Outstanding&quot;, laa.fee_charges_overdue_derived  AS &quot;Fees Overdue&quot;, l.penalty_charges_repaid_derived AS &quot;Penalties Repaid&quot;, l.penalty_charges_outstanding_derived  AS &quot;Penalties Outstanding&quot;, penalty_charges_overdue_derived  AS &quot;Penalties Overdue&quot; FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE Concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE Concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan l ON        l.client_id = c.id JOIN      m_product_loan pl ON        pl.id = l.product_id LEFT JOIN m_staff lo ON        lo.id = l.loan_officer_id LEFT JOIN m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_fund f ON        f.id = l.fund_id LEFT JOIN m_loan_arrears_aging laa ON        laa.loan_id = l.id WHERE     o.id = '${officeId}' AND       ( l.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') AND       ( l.product_id = '${loanProductId}' OR        '-1' = '${loanProductId}') AND       ( Coalesce(l.loan_officer_id, -10) = ${loanOfficerId} OR        '-1' = ${loanOfficerId}) AND       ( coalesce(l.fund_id, -10) = ${fundId} OR        -1 = ${fundId}) AND       ( coalesce(l.loanpurpose_cv_id, -10) = ${loanPurposeId} OR        -1 = ${loanPurposeId}) AND       l.loan_status_id = 300 GROUP BY  l.id, ounder.hierarchy , ounder.name, cur.display_symbol, lo.display_name, c.display_name, pl.name, f.name, laa.principal_overdue_derived, laa.interest_overdue_derived, laa.fee_charges_overdue_derived, laa.penalty_charges_overdue_derived , c.account_no ORDER BY  ounder.hierarchy, l.currency_code, c.account_no, l.account_no"/>
            <column name="description" value="Individual Client   Report"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="13"/>
            <column name="report_name" value="Obligation Met Loans Details"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT    Concat(REPEAT('..', ((Length(ounder.hierarchy ) - Length(REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS &quot;Office/Branch&quot;, Coalesce(cur.display_symbol, l.currency_code) AS currency, c.account_no  AS &quot;Client Account No.&quot;, c.display_name  AS &quot;Client&quot;, l.account_no  AS &quot;Loan Account No.&quot;, pl.name AS &quot;Product&quot;, f.name  AS fund, l.principal_amount  AS &quot;Loan Amount&quot;, l.total_repayment_derived AS &quot;Total Repaid&quot;, l.annual_nominal_interest_rate  AS &quot;Annual Nominal Interest Rate&quot;, DATE_TRUNC('day', l.disbursedon_date) AS &quot;Disbursed&quot;, DATE_TRUNC('day', l.closedon_date)  AS &quot;Closed&quot;, l.principal_repaid_derived  AS &quot;Principal Repaid&quot;, l.interest_repaid_derived AS &quot;Interest Repaid&quot;, l.fee_charges_repaid_derived  AS &quot;Fees Repaid&quot;, l.penalty_charges_repaid_derived  AS &quot;Penalties Repaid&quot;, lo.display_name AS &quot;Loan Officer&quot; FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE Concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE Concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan l ON        l.client_id = c.id JOIN      m_product_loan pl ON        pl.id = l.product_id LEFT JOIN m_staff lo ON        lo.id = l.loan_officer_id LEFT JOIN m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_fund f ON        f.id = l.fund_id WHERE     o.id = '${officeId}' AND       ( l.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') AND       ( l.product_id = '${loanProductId}' OR        '-1' = '${loanProductId}') AND       ( Coalesce(l.loan_officer_id, -10) = ${loanOfficerId} OR        '-1' = ${loanOfficerId}) AND       ( coalesce(l.fund_id, -10) = ${fundId} OR        -1 = ${fundId}) AND       ( coalesce(l.loanpurpose_cv_id, -10) = ${loanPurposeId} OR        -1 = ${loanPurposeId}) AND       ( CASE WHEN ${obligDateType} = 1 THEN l.closedon_date BETWEEN '${startDate}' AND '${endDate}' WHEN ${obligDateType} = 2 THEN l.disbursedon_date BETWEEN '${startDate}' AND '${endDate}' ELSE 1 = 1 end) AND       l.loan_status_id = 600 GROUP BY  l.id, ounder.hierarchy, ounder.name, cur.display_symbol, c.account_no, c.display_name, pl.name, f.name, lo.display_name ORDER BY  ounder.hierarchy, l.currency_code, c.account_no, l.account_no"/>
            <column name="description" value="Individual Client   Report"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="14"/>
            <column name="report_name" value="Obligation Met Loans Summary"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT    Concat(REPEAT('..', ((Length(ounder.hierarchy ) - Length(REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS &quot;Office/Branch&quot;, Coalesce(cur.display_symbol, l.currency_code) AS currency, Count(DISTINCT(c.id)) AS &quot;No. of Clients&quot;, Count(DISTINCT(l.id)) AS &quot;No. of Loans&quot;, Sum(l.principal_amount) AS &quot;Total Loan Amount&quot;, Sum(l.principal_repaid_derived) AS &quot;Total Principal Repaid&quot;, Sum(l.interest_repaid_derived)  AS &quot;Total Interest Repaid&quot;, Sum(l.fee_charges_repaid_derived) AS &quot;Total Fees Repaid&quot;, Sum(l.penalty_charges_repaid_derived) AS &quot;Total Penalties Repaid&quot;, Sum(l.interest_waived_derived)  AS &quot;Total Interest Waived&quot;, Sum(l.fee_charges_waived_derived) AS &quot;Total Fees Waived&quot;, Sum(l.penalty_charges_waived_derived) AS &quot;Total Penalties Waived&quot; FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan l ON        l.client_id = c.id JOIN      m_product_loan pl ON        pl.id = l.product_id LEFT JOIN m_staff lo ON        lo.id = l.loan_officer_id LEFT JOIN m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_fund f ON        f.id = l.fund_id WHERE     o.id = '${officeId}' AND       ( l.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') AND       ( l.product_id = '${loanProductId}' OR        '-1' = '${loanProductId}') AND       ( coalesce(l.loan_officer_id, -10) = ${loanOfficerId} OR        '-1' = ${loanOfficerId}) AND       ( coalesce(l.fund_id, -10) = ${fundId} OR        -1 = ${fundId}) AND       ( coalesce(l.loanpurpose_cv_id, -10) = ${loanPurposeId} OR        -1 = ${loanPurposeId}) AND       ( CASE WHEN ${obligDateType} = 1 THEN l.closedon_date BETWEEN '${startDate}' AND '${endDate}' WHEN ${obligDateType} = 2 THEN l.disbursedon_date BETWEEN '${startDate}' AND '${endDate}' ELSE 1 = 1 end) AND       l.loan_status_id = 600 GROUP BY  ounder.hierarchy, l.currency_code, ounder.name, cur.display_symbol ORDER BY  ounder.hierarchy, l.currency_code"/>
            <column name="description" value="Individual Client   Report"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="15"/>
            <column name="report_name" value="Portfolio at Risk"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT x.&quot;Currency&quot;, x.&quot;Principal Outstanding&quot;, x.&quot;Principal Overdue&quot;, x.&quot;Interest Outstanding&quot;, x.&quot;Interest Overdue&quot;, x.&quot;Fees Outstanding&quot;, x.&quot;Fees Overdue&quot;, x.&quot;Penalties Outstanding&quot;, x.&quot;Penalties Overdue&quot;, ( CASE WHEN ${parType} = 1 THEN cast(round((x.&quot;Principal Overdue&quot; * 100) / x.&quot;Principal Outstanding&quot;, 2) AS CHAR) WHEN ${parType} = 2 THEN cast(round(((x.&quot;Principal Overdue&quot; + x.&quot;Interest Overdue&quot;) * 100) / (x.&quot;Principal Outstanding&quot; + x.&quot;Interest Outstanding&quot;), 2) AS CHAR) WHEN ${parType} = 3 THEN cast(round(((x.&quot;Principal Overdue&quot; + x.&quot;Interest Overdue&quot; + x.&quot;Fees Overdue&quot;) * 100) / (x.&quot;Principal Outstanding&quot; + x.&quot;Interest Outstanding&quot; + x.&quot;Fees Outstanding&quot;), 2) AS CHAR) WHEN ${parType} = 4 THEN cast(round(((x.&quot;Principal Overdue&quot; + x.&quot;Interest Overdue&quot; + x.&quot;Fees Overdue&quot; + x.&quot;Penalties Overdue&quot;) * 100) / (x.&quot;Principal Outstanding&quot; + x.&quot;Interest Outstanding&quot; + x.&quot;Fees Outstanding&quot; + x.&quot;Penalties Overdue&quot;), 2) AS CHAR) ELSE 'invalid PAR Type' end) AS &quot;Portfolio at Risk %&quot; FROM   ( SELECT    coalesce(cur.display_symbol, l.currency_code) AS &quot;Currency&quot;, sum(l.principal_outstanding_derived)  AS &quot;Principal Outstanding&quot;, sum(laa.principal_overdue_derived)  AS &quot;Principal Overdue&quot;, sum(l.interest_outstanding_derived) AS &quot;Interest Outstanding&quot;, sum(laa.interest_overdue_derived) AS &quot;Interest Overdue&quot;, sum(l.fee_charges_outstanding_derived)  AS &quot;Fees Outstanding&quot;, sum(laa.fee_charges_overdue_derived)  AS &quot;Fees Overdue&quot;, sum(penalty_charges_outstanding_derived)  AS &quot;Penalties Outstanding&quot;, sum(laa.penalty_charges_overdue_derived)  AS &quot;Penalties Overdue&quot; FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan l ON        l.client_id = c.id LEFT JOIN m_staff lo ON        lo.id = l.loan_officer_id LEFT JOIN m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_fund f ON        f.id = l.fund_id LEFT JOIN m_code_value purp ON        purp.id = l.loanpurpose_cv_id LEFT JOIN m_product_loan p ON        p.id = l.product_id LEFT JOIN m_loan_arrears_aging laa ON        laa.loan_id = l.id WHERE     o.id = '${officeId}' AND       ( l.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') AND       ( l.product_id = '${loanProductId}' OR        '-1' = '${loanProductId}') AND       ( coalesce(l.loan_officer_id, -10) = ${loanOfficerId} OR        '-1' = ${loanOfficerId}) AND       ( coalesce(l.fund_id, -10) = ${fundId} OR        -1 = ${fundId}) and (coalesce(l.loanpurpose_cv_id, -10) = ${loanPurposeId} OR        -1 = ${loanPurposeId}) AND       l.loan_status_id = 300 GROUP BY  l.currency_code, cur.display_symbol ORDER BY  l.currency_code) x"/>
            <column name="description" value="Covers all loans.    For larger MFIs … we should add some derived fields on loan (or a 1:1 loan related table like mifos 2.x does)  Principle, Interest, Fees, Penalties Outstanding and Overdue (possibly waived and written off too)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="16"/>
            <column name="report_name" value="Portfolio at Risk by Branch"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT   Concat(REPEAT('..', ((Length(mo.hierarchy ) - Length(REPLACE(mo.hierarchy, '.', '')) - 1))), mo.name) AS &quot;Office/Branch&quot;, x.&quot;Currency&quot;, x.&quot;Principal Outstanding&quot; , x.&quot;Principal Overdue&quot;, x.&quot;Interest Outstanding&quot;, x.&quot;Interest Overdue&quot;, x.&quot;Fees Outstanding&quot;, x.&quot;Fees Overdue&quot;, x.&quot;Penalties Outstanding&quot;, x.&quot;Penalties Overdue&quot;, ( CASE WHEN ${parType} = 1 THEN cast(round((x.&quot;Principal Overdue&quot; * 100) / x.&quot;Principal Outstanding&quot;, 2) AS  CHAR) WHEN ${parType} = 2 THEN cast(round(((x.&quot;Principal Overdue&quot; + x.&quot;Interest Overdue&quot;) * 100) / (x.&quot;Principal Outstanding&quot; + x.&quot;Interest Outstanding&quot;), 2) AS      CHAR) WHEN ${parType} = 3 THEN cast(round(((x.&quot;Principal Overdue&quot; + x.&quot;Interest Overdue&quot; + x.&quot;Fees Overdue&quot;) * 100) / (x.&quot;Principal Outstanding&quot; + x.&quot;Interest Outstanding&quot; + x.&quot;Fees Outstanding&quot;), 2) AS CHAR) WHEN ${parType} = 4 THEN cast(round(((x.&quot;Principal Overdue&quot; + x.&quot;Interest Overdue&quot; + x.&quot;Fees Overdue&quot; + x.&quot;Penalties Overdue&quot;) * 100) / (x.&quot;Principal Outstanding&quot; + x.&quot;Interest Outstanding&quot; + x.&quot;Fees Outstanding&quot; + x.&quot;Penalties Overdue&quot;), 2) AS           CHAR) ELSE 'invalid PAR Type' end) AS &quot;Portfolio at Risk %&quot; FROM     m_office mo JOIN ( SELECT    ounder.id  AS &quot;branch&quot;, coalesce(cur.display_symbol, l.currency_code) AS &quot;Currency&quot;, sum(l.principal_outstanding_derived)  AS &quot;Principal Outstanding&quot;, sum(laa.principal_overdue_derived)  AS &quot;Principal Overdue&quot;, sum(l.interest_outstanding_derived) AS &quot;Interest Outstanding&quot;, sum(laa.interest_overdue_derived) AS &quot;Interest Overdue&quot;, sum(l.fee_charges_outstanding_derived)  AS &quot;Fees Outstanding&quot;, sum(laa.fee_charges_overdue_derived)  AS &quot;Fees Overdue&quot;, sum(penalty_charges_outstanding_derived)  AS &quot;Penalties Outstanding&quot;, sum(laa.penalty_charges_overdue_derived)  AS &quot;Penalties Overdue&quot; FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan l ON        l.client_id = c.id LEFT JOIN m_staff lo ON        lo.id = l.loan_officer_id LEFT JOIN m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_fund f ON        f.id = l.fund_id LEFT JOIN m_code_value purp ON        purp.id = l.loanpurpose_cv_id LEFT JOIN m_product_loan p ON        p.id = l.product_id LEFT JOIN m_loan_arrears_aging laa ON        laa.loan_id = l.id WHERE     o.id = '${officeId}' AND       ( l.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') AND       ( l.product_id = '${loanProductId}' OR        '-1' = '${loanProductId}') AND       ( coalesce(l.loan_officer_id, -10) = ${loanOfficerId} OR        '-1' = ${loanOfficerId}) AND       ( coalesce(l.fund_id, -10) = ${fundId} OR        -1 = ${fundId}) and (coalesce(l.loanpurpose_cv_id, -10) = ${loanPurposeId} OR        -1 = ${loanPurposeId}) AND       l.loan_status_id = 300 GROUP BY  ounder.id, l.currency_code, cur.display_symbol ) x ON       x.branch = mo.id ORDER BY mo.hierarchy, x.&quot;Currency&quot;"/>
            <column name="description" value="Covers all loans.    For larger MFIs … we should add some derived fields on loan (or a 1:1 loan related table like mifos 2.x does)  Principle, Interest, Fees, Penalties Outstanding and Overdue (possibly waived and written off too)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="20"/>
            <column name="report_name" value="Funds Disbursed Between Dates Summary"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Fund"/>
            <column name="report_sql" value="SELECT    Coalesce(f.name, '-') AS fund, Coalesce(cur.display_symbol, l.currency_code) AS currency, Round(Sum(l.principal_amount), 4) AS disbursed_amount FROM      m_office ounder JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan l ON        l.client_id = c.id JOIN      m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_fund f ON        f.id = l.fund_id WHERE     disbursedon_date BETWEEN '${startDate}' AND '${endDate}' AND       ( coalesce(l.fund_id, -10) = ${fundId} OR        -1 = ${fundId}) AND       ( l.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') AND       ounder.hierarchy LIKE concat('${currentUserHierarchy}', '%') GROUP BY  coalesce(f.name, '-') , coalesce(cur.display_symbol, l.currency_code) ORDER BY  coalesce(f.name, '-') , coalesce(cur.display_symbol, l.currency_code)"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="21"/>
            <column name="report_name" value="Funds Disbursed Between Dates Summary by Office"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Fund"/>
            <column name="report_sql" value="SELECT    Concat(REPEAT('..', ((Length(ounder.hierarchy) - Length(REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS &quot;Office/Branch&quot;, Coalesce(f.name, '-')  AS fund, Coalesce(cur.display_symbol, l.currency_code)  AS currency, Round(Sum(l.principal_amount), 4)  AS disbursed_amount FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE Concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE Concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan l ON        l.client_id = c.id JOIN      m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_fund f ON        f.id = l.fund_id WHERE     disbursedon_date BETWEEN '${startDate}' AND '${endDate}' AND       o.id = '${officeId}' AND       ( coalesce(l.fund_id, -10) = ${fundId} OR        -1 = ${fundId}) AND       ( l.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') GROUP BY  ounder.name, coalesce(f.name, '-') , coalesce(cur.display_symbol, l.currency_code), ounder.hierarchy ORDER BY  ounder.name, coalesce(f.name, '-') , coalesce(cur.display_symbol, l.currency_code)"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="48"/>
            <column name="report_name" value="Balance Sheet"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Accounting"/>
            <column name="report_sql"/>
            <column name="description" value="Balance Sheet"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="49"/>
            <column name="report_name" value="Income Statement"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Accounting"/>
            <column name="report_sql"/>
            <column name="description" value="Profit and Loss Statement"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="50"/>
            <column name="report_name" value="Trial Balance"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Accounting"/>
            <column name="report_sql"/>
            <column name="description" value="Trial Balance Report"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="51"/>
            <column name="report_name" value="Written-Off Loans"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT    Concat(REPEAT('..', ((Length(ounder.hierarchy) - Length(REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS &quot;Office/Branch&quot;, Coalesce(cur.display_symbol, ml.currency_code) AS currency, c.account_no AS &quot;Client Account No.&quot;, c.display_name AS &quot;Client Name&quot;, ml.account_no  AS &quot;Loan Account No.&quot;, mpl.name AS &quot;Product Name&quot;, ml.disbursedon_date  AS &quot;Disbursed Date&quot;, lt.transaction_date  AS &quot;Written Off date&quot;, ml.principal_amount  AS &quot;Loan Amount&quot;, Coalesce(lt.principal_portion_derived, 0)  AS &quot;Written-Off                    Principal&quot;, Coalesce(lt.interest_portion_derived, 0) AS &quot;Written-Off Interest&quot;, Coalesce(lt.fee_charges_portion_derived,0) AS &quot;Written-Off                    Fees&quot;, Coalesce(lt.penalty_charges_portion_derived,0) AS &quot;Written-Off Penalties&quot;, n.note AS &quot;Reason For Write-Off&quot;, Coalesce(ms.display_name,'-')  AS &quot;Loan Officer Name&quot; FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE Concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE Concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan ml ON        ml.client_id = c.id JOIN      m_product_loan mpl ON        mpl.id=ml.product_id LEFT JOIN m_staff ms ON        ms.id=ml.loan_officer_id JOIN      m_loan_transaction lt ON        lt.loan_id = ml.id LEFT JOIN m_note n ON        n.loan_transaction_id = lt.id LEFT JOIN m_currency cur ON        cur.code = ml.currency_code WHERE     lt.transaction_type_enum = 6 /*write-off */ AND       lt.is_reversed IS FALSE AND       ml.loan_status_id=601 AND       o.id='${officeId}' AND       ( mpl.id='${loanProductId}' OR        '${loanProductId}'=-1) AND       lt.transaction_date BETWEEN '${startDate}' AND '${endDate}' AND       ( ml.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') ORDER BY  ounder.hierarchy, coalesce(cur.display_symbol, ml.currency_code), ml.account_no"/>
            <column name="description" value="Individual Lending Report. Written Off Loans"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="52"/>
            <column name="report_name" value="Aging Detail"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT     Concat(Repeat('..', ((Length(ounder.hierarchy) - Length(Replace(ounder.hierarchy , '.', '')) - 1))), ounder.NAME) AS &quot;Office/Branch&quot;, COALESCE(cur.display_symbol, ml.currency_code)  AS currency, mc.account_no AS &quot;Client Account No.&quot;, mc.display_name  AS &quot;Client Name&quot;, ml.account_no  AS &quot;Account Number&quot;, ml.principal_amount  AS &quot;Loan Amount&quot;, ml.principal_disbursed_derived  AS &quot;Original Principal&quot;, ml.interest_charged_derived AS &quot;Original Interest&quot;, ml.principal_repaid_derived AS &quot;Principal Paid&quot;, ml.interest_repaid_derived  AS &quot;Interest Paid&quot;, laa.principal_overdue_derived AS &quot;Principal Overdue&quot;, laa.interest_overdue_derived  AS &quot;Interest Overdue&quot;, Extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))  AS &quot;Days in Arrears&quot;, CASE WHEN Extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;7 THEN '&lt;1' WHEN extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;8 THEN ' 1' WHEN extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;15 THEN '2' WHEN extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;22 THEN ' 3' WHEN extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;29 THEN ' 4' WHEN extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;36 THEN ' 5' WHEN extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;43 THEN ' 6' WHEN extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;50 THEN ' 7' WHEN extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;57 THEN ' 8' WHEN extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;64 THEN ' 9' WHEN extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;71 THEN '10' WHEN extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;78 THEN '11' WHEN extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;85 THEN '12' ELSE '12+' END AS &quot;Weeks In Arrears Band&quot;, CASE WHEN extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;31 THEN '0 - 30' WHEN extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;61 THEN '30 - 60' WHEN extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;91 THEN '60 - 90' WHEN extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;181 THEN '90 - 180' WHEN extract(day FROM (CURRENT_DATE::timestamp - laa.overdue_since_date_derived::timestamp))&lt;361 THEN '180 - 360' ELSE '&gt; 360' END AS &quot;Days in Arrears Band&quot; FROM       m_office mo JOIN       m_office ounder ON         ounder.hierarchy LIKE concat(mo.hierarchy, '%') AND        ounder.hierarchy LIKE concat('${currentUserHierarchy}', '%') INNER JOIN m_client mc ON         mc.office_id=ounder.id INNER JOIN m_loan ml ON         ml.client_id = mc.id INNER JOIN r_enum_value rev ON         rev.enum_id=ml.loan_status_id AND        rev.enum_name = 'loan_status_id' INNER JOIN m_loan_arrears_aging laa ON         laa.loan_id=ml.id LEFT JOIN  m_currency cur ON         cur.code = ml.currency_code WHERE      ml.loan_status_id=300 AND        mo.id='${officeId}' GROUP BY   ounder.hierarchy, ounder.name, cur.display_symbol, ml.currency_code, mc.account_no, mc.display_name, ml.account_no, ml.principal_amount, ml.principal_disbursed_derived, ml.interest_charged_derived, ml.principal_repaid_derived, ml.interest_repaid_derived, laa.principal_overdue_derived, laa.interest_overdue_derived, laa.overdue_since_date_derived ORDER BY   ounder.hierarchy, COALESCE(cur.display_symbol, ml.currency_code), ml.account_no"/>
            <column name="description" value="Loan arrears aging (Weeks)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="53"/>
            <column name="report_name" value="Aging Summary (Arrears in Weeks)"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT    Coalesce(periods.currencyname, periods.currency) AS currency, periods.period_no AS &quot;Weeks In Arrears (Up To)&quot;, coalesce(ars.loanid, 0) AS &quot;No Of Loans&quot;, coalesce(ars.principal,0.0) AS &quot;Original Principal&quot;, coalesce(ars.interest,0.0) AS &quot;Original Interest&quot;, coalesce(ars.prinpaid,0.0) AS &quot;Principal Paid&quot;, coalesce(ars.intpaid,0.0) AS &quot;Interest Paid&quot;, coalesce(ars.prinoverdue,0.0) AS &quot;Principal Overdue&quot;, coalesce(ars.intoverdue,0.0) AS &quot;Interest Overdue&quot; FROM      ( SELECT curs.code AS currency, curs.name AS currencyname, pers.* FROM   ( SELECT 'On Schedule' period_no, 1             pid UNION SELECT '1', 2 UNION SELECT '2', 3 UNION SELECT '3', 4 UNION SELECT '4', 5 UNION SELECT '5', 6 UNION SELECT '6', 7 UNION SELECT '7', 8 UNION SELECT '8', 9 UNION SELECT '9', 10 UNION SELECT '10', 11 UNION SELECT '11', 12 UNION SELECT '12', 13 UNION SELECT '12+', 14) pers, ( SELECT     DISTINCT ON (moc.code) moc.code, moc.name FROM       m_office mo2 INNER JOIN m_office ounder2 ON         ounder2.hierarchy LIKE concat(mo2.hierarchy, '%') AND        ounder2.hierarchy LIKE concat('${currentUserHierarchy}', '%') INNER JOIN m_client mc2 ON         mc2.office_id=ounder2.id INNER JOIN m_loan ml2 ON         ml2.client_id = mc2.id INNER JOIN m_organisation_currency moc ON         moc.code = ml2.currency_code WHERE      ml2.loan_status_id=300 /* active */ AND        mo2.id='${officeId}' AND        ( ml2.currency_code = '${currencyId}' OR         '-1' = '${currencyId}') GROUP BY moc.code, moc.name) curs) periods LEFT JOIN ( SELECT   z.currency, z.arrperiod, count(z.loanid)  AS loanid, sum(z.principal) AS principal, sum(z.interest)  AS interest, sum(z.prinpaid)  AS prinpaid, sum(z.intpaid) AS intpaid, sum(z.prinoverdue) AS prinoverdue, sum(z.intoverdue)  AS intoverdue FROM     ( SELECT x.loanid, x.currency, x.principal, x.interest, x.prinpaid, x.intpaid, x.prinoverdue, x.intoverdue, CASE WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;1 THEN 'On Schedule' WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;8 THEN '1' WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;15 THEN '2' WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;22 THEN '3' WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;29 THEN '4' WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;36 THEN '5' WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;43 THEN '6' WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;50 THEN '7' WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;57 THEN '8' WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;64 THEN '9' WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;71 THEN '10' WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;78 THEN '11' WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;85 THEN '12' ELSE '12+' end AS arrperiod FROM   ( SELECT     ml.id  AS loanid, ml.currency_code AS currency, ml.principal_disbursed_derived AS principal, ml.interest_charged_derived  AS interest, ml.principal_repaid_derived  AS prinpaid, ml.interest_repaid_derived  AS intpaid, laa.principal_overdue_derived  AS prinoverdue, laa.interest_overdue_derived AS intoverdue, coalesce(laa.overdue_since_date_derived, CURRENT_DATE) AS minoverduedate FROM       m_office mo INNER JOIN m_office ounder ON         ounder.hierarchy LIKE concat(mo.hierarchy, '%') AND        ounder.hierarchy LIKE concat('${currentUserHierarchy}', '%') INNER JOIN m_client mc ON         mc.office_id=ounder.id INNER JOIN m_loan ml ON         ml.client_id = mc.id LEFT JOIN  m_loan_arrears_aging laa ON         laa.loan_id = ml.id WHERE      ml.loan_status_id=300 AND        mo.id='${officeId}' AND        ( ml.currency_code = '${currencyId}' OR         '-1' = '${currencyId}') GROUP BY   ml.id, laa.principal_overdue_derived, laa.interest_overdue_derived, laa.overdue_since_date_derived) x ) z GROUP BY z.currency, z.arrperiod ) ars ON        ars.arrperiod=periods.period_no AND       ars.currency = periods.currency ORDER BY  periods.currency, periods.pid"/>
            <column name="description" value="Loan amount in arrears by branch"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="54"/>
            <column name="report_name" value="Rescheduled Loans"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT    Concat(REPEAT('..', ((Length(ounder.hierarchy) - Length(REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS &quot;Office/Branch&quot;, Coalesce(cur.display_symbol, ml.currency_code) AS currency, c.account_no AS &quot;Client Account No.&quot;, c.display_name AS &quot;Client Name&quot;, ml.account_no  AS &quot;Loan Account No.&quot;, mpl.name AS &quot;Product Name&quot;, ml.disbursedon_date  AS &quot;Disbursed Date&quot;, lt.transaction_date  AS &quot;Written Off date&quot;, ml.principal_amount  AS &quot;Loan Amount&quot;, Coalesce(lt.principal_portion_derived, 0)  AS &quot;Rescheduled Principal&quot;, Coalesce(lt.interest_portion_derived, 0) AS &quot;Rescheduled Interest&quot;, Coalesce(lt.fee_charges_portion_derived,0) AS &quot;Rescheduled Fees&quot;, Coalesce(lt.penalty_charges_portion_derived,0) AS &quot;Rescheduled Penalties&quot;, n.note AS &quot;Reason For Rescheduling&quot;, Coalesce(ms.display_name,'-')  AS &quot;Loan Officer Name&quot; FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE Concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE Concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan ml ON        ml.client_id = c.id JOIN      m_product_loan mpl ON        mpl.id=ml.product_id LEFT JOIN m_staff ms ON        ms.id=ml.loan_officer_id JOIN      m_loan_transaction lt ON        lt.loan_id = ml.id LEFT JOIN m_note n ON        n.loan_transaction_id = lt.id LEFT JOIN m_currency cur ON        cur.code = ml.currency_code WHERE     lt.transaction_type_enum = 7 /*marked for rescheduling */ AND       lt.is_reversed IS FALSE AND       ml.loan_status_id=602 AND       o.id='${officeId}' AND       ( mpl.id='${loanProductId}' OR        '${loanProductId}'=-1) AND       lt.transaction_date BETWEEN '${startDate}' AND '${endDate}' AND       ( ml.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') ORDER BY  ounder.hierarchy, coalesce(cur.display_symbol, ml.currency_code), ml.account_no"/>
            <column name="description" value="Individual Lending Report. Rescheduled Loans.  The ability to reschedule (or mark that you have rescheduled the loan elsewhere) is a legacy of the older Mifos product.  Needed for migration."/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="55"/>
            <column name="report_name" value="Active Loans Passed Final Maturity"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT    Concat(REPEAT('..', ((Length(ounder.hierarchy) - Length(REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS &quot;Office/Branch&quot;, Coalesce(cur.display_symbol, l.currency_code)  AS currency, lo.display_name  AS &quot;Loan Officer&quot;, c.display_name AS &quot;Client&quot;, l.account_no AS &quot;Loan Account No.&quot;, pl.name  AS &quot;Product&quot;, f.name AS fund, l.principal_amount AS &quot;Loan Amount&quot;, l.annual_nominal_interest_rate AS &quot;Annual Nominal Interest Rate&quot;, DATE_TRUNC('day', l.disbursedon_date)  AS &quot;Disbursed Date&quot;, DATE_TRUNC('day', l.expected_maturedon_date) AS &quot;Expected Matured On&quot;, l.principal_repaid_derived AS &quot;Principal Repaid&quot;, l.principal_outstanding_derived  AS &quot;Principal Outstanding&quot;, laa.principal_overdue_derived  AS &quot;Principal Overdue&quot;, l.interest_repaid_derived  AS &quot;Interest Repaid&quot;, l.interest_outstanding_derived AS &quot;Interest Outstanding&quot;, laa.interest_overdue_derived AS &quot;Interest Overdue&quot;, l.fee_charges_repaid_derived AS &quot;Fees Repaid&quot;, l.fee_charges_outstanding_derived  AS &quot;Fees Outstanding&quot;, laa.fee_charges_overdue_derived  AS &quot;Fees Overdue&quot;, l.penalty_charges_repaid_derived AS &quot;Penalties Repaid&quot;, l.penalty_charges_outstanding_derived  AS &quot;Penalties Outstanding&quot;, laa.penalty_charges_overdue_derived  AS &quot;Penalties Overdue&quot; FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE Concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE Concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan l ON        l.client_id = c.id JOIN      m_product_loan pl ON        pl.id = l.product_id LEFT JOIN m_staff lo ON        lo.id = l.loan_officer_id LEFT JOIN m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_fund f ON        f.id = l.fund_id LEFT JOIN m_loan_arrears_aging laa ON        laa.loan_id = l.id WHERE     o.id = '${officeId}' AND       ( l.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') AND       ( l.product_id = '${loanProductId}' OR        '-1' = '${loanProductId}') AND       ( Coalesce(l.loan_officer_id, -10) = ${loanOfficerId} OR        '-1' = ${loanOfficerId}) AND       ( coalesce(l.fund_id, -10) = ${fundId} OR        -1 = ${fundId}) AND       ( coalesce(l.loanpurpose_cv_id, -10) = ${loanPurposeId} OR        -1 = ${loanPurposeId}) AND       l.loan_status_id = 300 AND       l.expected_maturedon_date &lt; CURRENT_DATE GROUP BY  l.id, ounder.hierarchy, ounder.name, cur.display_symbol, lo.display_name, c.display_name, f.name, pl.name, laa.principal_overdue_derived, laa.interest_overdue_derived, laa.fee_charges_overdue_derived, laa.penalty_charges_overdue_derived, c.account_no ORDER BY  ounder.hierarchy, l.currency_code, c.account_no, l.account_no"/>
            <column name="description" value="Individual Client   Report"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="56"/>
            <column name="report_name" value="Active Loans Passed Final Maturity Summary"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT   Concat(REPEAT('..', ((Length(mo.hierarchy ) - Length(REPLACE(mo.hierarchy, '.', '')) - 1))), mo.name) AS &quot;Office/Branch&quot;, x.currency  AS currency, x.client_count  AS &quot;No. of Clients&quot;, x.active_loan_count AS &quot;No. Active                    Loans&quot;, x. arrears_loan_count AS &quot;No. of Loans in Arrears&quot;, x.principal AS &quot;Total Loans Disbursed&quot;, x.principal_repaid  AS &quot;Principal Repaid&quot;, x.principal_outstanding AS &quot;Principal Outstanding&quot;, x.principal_overdue AS &quot;Principal Overdue&quot;, x.interest  AS &quot;Total Interest&quot;, x.interest_repaid AS &quot;Interest Repaid&quot;, x.interest_outstanding  AS &quot;Interest Outstanding&quot;, x.interest_overdue  AS &quot;Interest Overdue&quot;, x.fees  AS &quot;Total Fees&quot;, x.fees_repaid AS &quot;Fees Repaid&quot;, x.fees_outstanding  AS &quot;Fees Outstanding&quot;, x.fees_overdue  AS &quot;Fees Overdue&quot;, x.penalties AS &quot;Total Penalties&quot;, x.penalties_repaid  AS &quot;Penalties Repaid&quot;, x.penalties_outstanding AS &quot;Penalties Outstanding&quot;, x.penalties_overdue AS &quot;Penalties Overdue&quot;, ( CASE WHEN ${parType} = 1 THEN cast(round((x.principal_overdue * 100) / x.principal_outstanding, 2) AS                                                                                                                                     CHAR) WHEN ${parType} = 2 THEN cast(round(((x.principal_overdue + x.interest_overdue) * 100) / (x.principal_outstanding + x.interest_outstanding), 2) AS                                                                                   CHAR) WHEN ${parType} = 3 THEN cast(round(((x.principal_overdue + x.interest_overdue + x.fees_overdue) * 100) / (x.principal_outstanding + x.interest_outstanding + x.fees_outstanding), 2) AS                                             CHAR) WHEN ${parType} = 4 THEN cast(round(((x.principal_overdue + x.interest_overdue + x.fees_overdue + x.penalties_overdue) * 100) / (x.principal_outstanding + x.interest_outstanding + x.fees_outstanding + x.penalties_overdue), 2) AS CHAR) ELSE 'invalid PAR Type' end) AS &quot;Portfolio at Risk %&quot; FROM     m_office mo JOIN ( SELECT    ounder.id  AS branch, coalesce(cur.display_symbol, l.currency_code)  AS currency, count(DISTINCT(c.id))  AS client_count, count(DISTINCT(l.id))  AS active_loan_count, count(DISTINCT(laa.loan_id) )  AS arrears_loan_count, sum(l.principal_disbursed_derived) AS principal, sum(l.principal_repaid_derived)  AS principal_repaid, sum(l.principal_outstanding_derived) AS principal_outstanding, sum(coalesce(laa.principal_overdue_derived,0)) AS principal_overdue, sum(l.interest_charged_derived)  AS interest, sum(l.interest_repaid_derived) AS interest_repaid, sum(l.interest_outstanding_derived)  AS interest_outstanding, sum(coalesce(laa.interest_overdue_derived,0))  AS interest_overdue, sum(l.fee_charges_charged_derived) AS fees, sum(l.fee_charges_repaid_derived)  AS fees_repaid, sum(l.fee_charges_outstanding_derived) AS fees_outstanding, sum(coalesce(laa.fee_charges_overdue_derived,0)) AS fees_overdue, sum(l.penalty_charges_charged_derived) AS penalties, sum(l.penalty_charges_repaid_derived)  AS penalties_repaid, sum(l.penalty_charges_outstanding_derived) AS penalties_outstanding, sum(coalesce(laa.penalty_charges_overdue_derived,0)) AS penalties_overdue FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan l ON        l.client_id = c.id LEFT JOIN m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_loan_arrears_aging laa ON        laa.loan_id = l.id WHERE     o.id = '${officeId}' AND       ( l.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') AND       ( l.product_id = '${loanProductId}' OR        '-1' = '${loanProductId}') AND       ( coalesce(l.loan_officer_id, -10) = ${loanOfficerId} OR        '-1' = ${loanOfficerId}) AND       ( coalesce(l.fund_id, -10) = ${fundId} OR        -1 = ${fundId}) and (coalesce(l.loanpurpose_cv_id, -10) = ${loanPurposeId} OR        -1 = ${loanPurposeId}) AND       l.loan_status_id = 300 AND       l.expected_maturedon_date &lt; CURRENT_DATE GROUP BY  ounder.id, cur.display_symbol, l.currency_code) x ON       x.branch = mo.id ORDER BY mo.hierarchy, x.currency"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="57"/>
            <column name="report_name" value="Active Loans in last installment"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT    Concat(REPEAT('..', ((Length(lastinstallment.hierarchy) - Length(REPLACE(lastinstallment.hierarchy, '.', '')) - 1))), lastinstallment.branch) AS &quot;Office/Branch&quot;, lastinstallment.currency, lastinstallment.&quot;Loan Officer&quot;, lastinstallment.&quot;Client Account No&quot;, lastinstallment.&quot;Client&quot;, lastinstallment.&quot;Loan Account No&quot;, lastinstallment.&quot;Product&quot;, lastinstallment.&quot;Fund&quot;, lastinstallment.&quot;Loan Amount&quot;, lastinstallment.&quot;Annual Nominal Interest Rate&quot;, lastinstallment.&quot;Disbursed&quot;, lastinstallment.&quot;Expected Matured On&quot; , l.principal_repaid_derived  AS &quot;Principal Repaid&quot;, l.principal_outstanding_derived AS &quot;Principal Outstanding&quot;, laa.principal_overdue_derived AS &quot;Principal Overdue&quot;, l.interest_repaid_derived AS &quot;Interest Repaid&quot;, l.interest_outstanding_derived  AS &quot;Interest Outstanding&quot;, laa.interest_overdue_derived  AS &quot;Interest Overdue&quot;, l.fee_charges_repaid_derived  AS &quot;Fees Repaid&quot;, l.fee_charges_outstanding_derived AS &quot;Fees Outstanding&quot;, laa.fee_charges_overdue_derived AS &quot;Fees Overdue&quot;, l.penalty_charges_repaid_derived  AS &quot;Penalties Repaid&quot;, l.penalty_charges_outstanding_derived AS &quot;Penalties Outstanding&quot;, laa.penalty_charges_overdue_derived AS &quot;Penalties Overdue&quot; FROM      ( SELECT    l.id AS loanid, l.number_of_repayments, Min(r.installment), ounder.id, ounder.hierarchy, ounder.name AS branch, Coalesce(cur.display_symbol, l.currency_code) AS currency, lo.display_name AS &quot;Loan Officer&quot;, c.account_no  AS &quot;Client Account No&quot;, c.display_name  AS &quot;Client&quot;, l.account_no  AS &quot;Loan Account No&quot;, pl.name AS &quot;Product&quot;, f.name  AS &quot;Fund&quot;, l.principal_amount  AS &quot;Loan Amount&quot;, l.annual_nominal_interest_rate  AS &quot;Annual Nominal Interest Rate&quot;, DATE_TRUNC('day', l.disbursedon_date) AS &quot;Disbursed&quot;, DATE_TRUNC('day', l.expected_maturedon_date)  AS &quot;Expected Matured On&quot; FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE Concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE Concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan l ON        l.client_id = c.id JOIN      m_product_loan pl ON        pl.id = l.product_id LEFT JOIN m_staff lo ON        lo.id = l.loan_officer_id LEFT JOIN m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_fund f ON        f.id = l.fund_id LEFT JOIN m_loan_repayment_schedule r ON        r.loan_id = l.id WHERE     o.id = '${officeId}' AND       ( l.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') AND       ( l.product_id = '${loanProductId}' OR        '-1' = '${loanProductId}') AND       ( Coalesce(l.loan_officer_id, -10) = ${loanOfficerId} OR        '-1' = ${loanOfficerId}) AND       ( coalesce(l.fund_id, -10) = ${fundId} OR        -1 = ${fundId}) AND       ( coalesce(l.loanpurpose_cv_id, -10) = ${loanPurposeId} OR        -1 = ${loanPurposeId}) AND       l.loan_status_id = 300 AND       r.completed_derived IS FALSE AND       r.duedate &gt;= CURRENT_DATE GROUP BY  l.id, ounder.id, cur.display_symbol, lo.display_name, c.account_no, c.display_name, pl.name, f.name HAVING    l.number_of_repayments = min(r.installment)) lastinstallment JOIN      m_loan l ON        l.id = lastinstallment.loanid LEFT JOIN m_loan_arrears_aging laa ON        laa.loan_id = l.id ORDER BY  lastinstallment.hierarchy, lastinstallment.currency, lastinstallment.&quot;Client Account No&quot;, lastinstallment.&quot;Loan Account No&quot;"/>
            <column name="description" value="Individual Client   Report"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="58"/>
            <column name="report_name" value="Active Loans in last installment Summary"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT   Concat(REPEAT('..', ((Length(mo.hierarchy ) - Length(REPLACE(mo.hierarchy, '.', '')) - 1))), mo.name) AS &quot;Office/Branch&quot;, x.currency  AS currency, x.client_count  AS &quot;No. of Clients&quot;, x.active_loan_count AS &quot;No. Active Loans&quot;, x. arrears_loan_count AS &quot;No. of Loans in Arrears&quot;, x.principal AS &quot;Total Loans Disbursed&quot;, x.principal_repaid  AS &quot;Principal Repaid&quot;, x.principal_outstanding AS &quot;Principal Outstanding&quot;, x.principal_overdue AS &quot;Principal Overdue&quot;, x.interest  AS &quot;Total Interest&quot;, x.interest_repaid AS &quot;Interest Repaid&quot;, x.interest_outstanding  AS &quot;Interest Outstanding&quot;, x.interest_overdue  AS &quot;Interest Overdue&quot;, x.fees  AS &quot;Total Fees&quot;, x.fees_repaid AS &quot;Fees Repaid&quot;, x.fees_outstanding  AS &quot;Fees Outstanding&quot;, x.fees_overdue  AS &quot;Fees Overdue&quot;, x.penalties AS &quot;Total Penalties&quot;, x.penalties_repaid  AS &quot;Penalties Repaid&quot;, x.penalties_outstanding AS &quot;Penalties Outstanding&quot;, x.penalties_overdue AS &quot;Penalties Overdue&quot;, ( CASE WHEN ${parType} = 1 THEN cast(round((x.principal_overdue * 100) / x.principal_outstanding, 2) AS                                                                                                                                     CHAR) WHEN ${parType} = 2 THEN cast(round(((x.principal_overdue + x.interest_overdue) * 100) / (x.principal_outstanding + x.interest_outstanding), 2) AS                                                                                   CHAR) WHEN ${parType} = 3 THEN cast(round(((x.principal_overdue + x.interest_overdue + x.fees_overdue) * 100) / (x.principal_outstanding + x.interest_outstanding + x.fees_outstanding), 2) AS                                             CHAR) WHEN ${parType} = 4 THEN cast(round(((x.principal_overdue + x.interest_overdue + x.fees_overdue + x.penalties_overdue) * 100) / (x.principal_outstanding + x.interest_outstanding + x.fees_outstanding + x.penalties_overdue), 2) AS CHAR) ELSE 'invalid PAR Type' end) AS &quot;Portfolio at Risk %&quot; FROM     m_office mo JOIN ( SELECT    lastinstallment.branchid AS branchid, lastinstallment.currency, count(DISTINCT(lastinstallment.clientid))  AS client_count, count(DISTINCT(lastinstallment.loanid))  AS active_loan_count, count(DISTINCT(laa.loan_id) )  AS arrears_loan_count, sum(l.principal_disbursed_derived) AS principal, sum(l.principal_repaid_derived)  AS principal_repaid, sum(l.principal_outstanding_derived) AS principal_outstanding, sum(coalesce(laa.principal_overdue_derived,0)) AS principal_overdue, sum(l.interest_charged_derived)  AS interest, sum(l.interest_repaid_derived) AS interest_repaid, sum(l.interest_outstanding_derived)  AS interest_outstanding, sum(coalesce(laa.interest_overdue_derived,0))  AS interest_overdue, sum(l.fee_charges_charged_derived) AS fees, sum(l.fee_charges_repaid_derived)  AS fees_repaid, sum(l.fee_charges_outstanding_derived) AS fees_outstanding, sum(coalesce(laa.fee_charges_overdue_derived,0)) AS fees_overdue, sum(l.penalty_charges_charged_derived) AS penalties, sum(l.penalty_charges_repaid_derived)  AS penalties_repaid, sum(l.penalty_charges_outstanding_derived) AS penalties_outstanding, sum(coalesce(laa.penalty_charges_overdue_derived,0)) AS penalties_overdue FROM      ( SELECT    l.id AS loanid, l.number_of_repayments, min(r.installment), ounder.id AS branchid, ounder.hierarchy, ounder.name AS branch, coalesce(cur.display_symbol, l.currency_code) AS currency, lo.display_name AS &quot;Loan Officer&quot;, c.id  AS clientid, c.account_no  AS &quot;Client Account No&quot;, c.display_name  AS &quot;Client&quot;, l.account_no  AS &quot;Loan Account No&quot;, pl.name AS &quot;Product&quot;, f.name  AS fund, l.principal_amount  AS &quot;Loan Amount&quot;, l.annual_nominal_interest_rate  AS &quot;Annual Nominal Interest Rate&quot;, DATE_TRUNC('day', l.disbursedon_date) AS &quot;Disbursed&quot;, DATE_TRUNC('day', l.expected_maturedon_date)  AS &quot;Expected Matured On&quot; FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan l ON        l.client_id = c.id JOIN      m_product_loan pl ON        pl.id = l.product_id LEFT JOIN m_staff lo ON        lo.id = l.loan_officer_id LEFT JOIN m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_fund f ON        f.id = l.fund_id LEFT JOIN m_loan_repayment_schedule r ON        r.loan_id = l.id WHERE     o.id = '${officeId}' AND       ( l.currency_code = '${currencyId}' OR        '-1' = '${currencyId}') AND       ( l.product_id = '${loanProductId}' OR        '-1' = '${loanProductId}') AND       ( coalesce(l.loan_officer_id, -10) = ${loanOfficerId} OR        '-1' = ${loanOfficerId}) AND       ( coalesce(l.fund_id, -10) = ${fundId} OR        -1 = ${fundId}) AND       ( coalesce(l.loanpurpose_cv_id, -10) = ${loanPurposeId} OR        -1 = ${loanPurposeId}) AND       l.loan_status_id = 300 AND       r.completed_derived IS FALSE AND       r.duedate &gt;= CURRENT_DATE GROUP BY  l.id, ounder.id, cur.display_symbol, lo.display_name, c.id, pl.name, f.name HAVING    l.number_of_repayments = min(r.installment)) lastinstallment JOIN      m_loan l ON        l.id = lastinstallment.loanid LEFT JOIN m_loan_arrears_aging laa ON        laa.loan_id = l.id GROUP BY  lastinstallment.branchid, lastinstallment.currency) x ON       x.branchid = mo.id ORDER BY mo.hierarchy, x.currency"/>
            <column name="description" value="Individual Client   Report"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="59"/>
            <column name="report_name" value="Active Loans by Disbursal Period"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT    Concat(REPEAT('..', ((Length(ounder.hierarchy) - Length(REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS &quot;Office/Branch&quot;, Coalesce(cur.display_symbol, l.currency_code)  AS currency, c.account_no AS &quot;Client Account No&quot;, c.display_name AS &quot;Client&quot;, l.account_no AS &quot;Loan Account No&quot;, pl.name  AS &quot;Product&quot;, f.name AS fund, l.principal_amount AS &quot;Loan Principal Amount&quot;, l.annual_nominal_interest_rate AS &quot;Annual Nominal Interest Rate&quot;, DATE_TRUNC('day', l.disbursedon_date)  AS &quot;Disbursed Date&quot;, l.total_expected_repayment_derived AS &quot;Total Loan (P+I+F+Pen)&quot;, l.total_repayment_derived  AS &quot;Total Repaid (P+I+F+Pen)&quot;, lo.display_name  AS &quot;Loan Officer&quot; FROM      m_office o JOIN      m_office ounder ON        ounder.hierarchy LIKE Concat(o.hierarchy, '%') AND       ounder.hierarchy LIKE Concat('${currentUserHierarchy}', '%') JOIN      m_client c ON        c.office_id = ounder.id JOIN      m_loan l ON        l.client_id = c.id JOIN      m_product_loan pl ON        pl.id = l.product_id LEFT JOIN m_staff lo ON        lo.id = l.loan_officer_id LEFT JOIN m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_fund f ON        f.id = l.fund_id LEFT JOIN m_loan_arrears_aging laa ON        laa.loan_id = l.id WHERE     o.id = '${officeId}' AND       (l.currency_code = '${currencyId}' OR '-1' = '${currencyId}') AND       (l.product_id = '${loanProductId}' OR '-1' = '${loanProductId}') AND       (Coalesce(l.loan_officer_id, -10) = ${loanOfficerId} OR '-1' = ${loanOfficerId}) AND       (coalesce(l.fund_id, -10) = ${fundId} OR -1 = ${fundId}) AND       (coalesce(l.loanpurpose_cv_id, -10) = ${loanPurposeId} OR -1 = ${loanPurposeId}) AND       l.disbursedon_date BETWEEN '${startDate}' AND '${endDate}' AND       l.loan_status_id = 300 GROUP BY  l.id, ounder.hierarchy, ounder.name, cur.display_symbol, c.account_no, c.display_name, pl.name, f.name, lo.display_name ORDER BY  ounder.hierarchy, l.currency_code, c.account_no, l.account_no"/>
            <column name="description" value="Individual Client   Report"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="61"/>
            <column name="report_name" value="Aging Summary (Arrears in Months)"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT    Coalesce(periods.currencyname, periods.currency) AS currency, periods.period_no AS &quot;Days In Arrears&quot;, coalesce(ars.loanid, 0) AS &quot;No Of Loans&quot;, coalesce(ars.principal,0.0) AS &quot;Original Principal&quot;, coalesce(ars.interest,0.0) AS &quot;Original Interest&quot;, coalesce(ars.prinpaid,0.0) AS &quot;Principal Paid&quot;, coalesce(ars.intpaid,0.0) AS &quot;Interest Paid&quot;, coalesce(ars.prinoverdue,0.0) AS &quot;Principal Overdue&quot;, coalesce(ars.intoverdue,0.0) AS &quot;Interest Overdue&quot; FROM ( SELECT curs.code AS currency, curs.name AS currencyname, pers.* FROM   ( SELECT 'On Schedule' period_no, 1 pid UNION SELECT '0 - 30', 2 UNION SELECT '30 - 60', 3 UNION SELECT '60 - 90', 4 UNION SELECT '90 - 180', 5 UNION SELECT '180 - 360', 6 UNION SELECT '&gt; 360', 7 ) pers, ( SELECT  DISTINCT ON (moc.code) moc.code, moc.name FROM       m_office mo2 INNER JOIN m_office ounder2 ON         ounder2.hierarchy LIKE concat(mo2.hierarchy, '%') AND        ounder2.hierarchy LIKE concat('${currentUserHierarchy}', '%') INNER JOIN m_client mc2 ON         mc2.office_id=ounder2.id INNER JOIN m_loan ml2 ON         ml2.client_id = mc2.id INNER JOIN m_organisation_currency moc ON         moc.code = ml2.currency_code WHERE      ml2.loan_status_id=300 /* active */ AND        mo2.id='${officeId}' AND        ( ml2.currency_code = '${currencyId}' OR         '-1' = '${currencyId}') GROUP BY moc.code, moc.name) curs) periods LEFT JOIN /* table of aging periods per currency with gaps if no applicable loans */ ( SELECT   z.currency, z.arrperiod, count(z.loanid)  AS loanid, sum(z.principal) AS principal, sum(z.interest)  AS interest, sum(z.prinpaid)  AS prinpaid, sum(z.intpaid) AS intpaid, sum(z.prinoverdue) AS prinoverdue, sum(z.intoverdue)  AS intoverdue FROM     ( SELECT x.loanid, x.currency, x.principal, x.interest, x.prinpaid, x.intpaid, x.prinoverdue, x.intoverdue, CASE WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;1 THEN 'On Schedule' WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;31 THEN '0 - 30' WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;61 THEN '30 - 60' WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;91 THEN '60 - 90' WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;181 THEN '90 - 180' WHEN extract(day FROM (CURRENT_DATE::TIMESTAMP - MINOVERDUEDATE::TIMESTAMP))&lt;361 THEN '180 - 360' ELSE '&gt; 360' end AS arrperiod FROM   ( SELECT     ml.id  AS loanid, ml.currency_code AS currency, ml.principal_disbursed_derived AS principal, ml.interest_charged_derived  AS interest, ml.principal_repaid_derived  AS prinpaid, ml.interest_repaid_derived AS intpaid, laa.principal_overdue_derived  AS prinoverdue, laa.interest_overdue_derived AS intoverdue, coalesce(laa.overdue_since_date_derived, CURRENT_DATE) AS minoverduedate FROM       m_office mo INNER JOIN m_office ounder ON         ounder.hierarchy LIKE concat(mo.hierarchy, '%') AND        ounder.hierarchy LIKE concat('${currentUserHierarchy}', '%') INNER JOIN m_client mc ON         mc.office_id=ounder.id INNER JOIN m_loan ml ON         ml.client_id = mc.id LEFT JOIN  m_loan_arrears_aging laa ON         laa.loan_id = ml.id WHERE      ml.loan_status_id=300 AND        mo.id='${officeId}' AND        ( ml.currency_code = '${currencyId}' OR         '-1' = '${currencyId}') GROUP BY   ml.id, laa.principal_overdue_derived, laa.interest_overdue_derived, laa.overdue_since_date_derived) x ) z GROUP BY z.currency, z.arrperiod ) ars ON        ars.arrperiod=periods.period_no AND       ars.currency = periods.currency ORDER BY  periods.currency, periods.pid"/>
            <column name="description" value="Loan amount in arrears by branch"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="91"/>
            <column name="report_name" value="Loan Account Schedule"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="92"/>
            <column name="report_name" value="Branch Expected Cash Flow"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="93"/>
            <column name="report_name" value="Expected Payments By Date - Basic"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT    ounder.name AS &quot;Office&quot;, coalesce(ms.display_name,'-') AS &quot;Loan Officer&quot;, mc.account_no AS &quot;Client Account Number&quot;, mc.display_name AS &quot;Name&quot;, mp.name AS &quot;Product&quot;, ml.account_no AS &quot;Loan Account Number&quot;, mr.duedate AS &quot;Due Date&quot;, mr.installment AS &quot;Installment&quot;, cu.display_symbol AS &quot;Currency&quot;, mr.principal_amount - coalesce(mr.principal_completed_derived,0) AS &quot;Principal Due&quot;, mr.interest_amount - coalesce(coalesce(mr.interest_completed_derived,mr.interest_waived_derived),0) AS &quot;Interest Due&quot;, coalesce(mr.fee_charges_amount,0) - coalesce(coalesce(mr.fee_charges_completed_derived,mr.fee_charges_waived_derived),0) AS &quot;Fees Due&quot;, coalesce(mr.penalty_charges_amount,0) - coalesce(coalesce(mr.penalty_charges_completed_derived,mr.penalty_charges_waived_derived),0) AS &quot;Penalty Due&quot;, (mr.principal_amount- coalesce(mr.principal_completed_derived,0)) + (mr.interest_amount- coalesce(coalesce(mr.interest_completed_derived,mr.interest_waived_derived),0)) + (coalesce(mr.fee_charges_amount,0)- coalesce(coalesce(mr.fee_charges_completed_derived,mr.fee_charges_waived_derived),0)) + (coalesce(mr.penalty_charges_amount,0)- coalesce(coalesce(mr.penalty_charges_completed_derived,mr.penalty_charges_waived_derived),0)) AS &quot;Total Due&quot;, mlaa.total_overdue_derived AS &quot;Total Overdue&quot; FROM      m_office mo JOIN      m_office ounder ON        ounder.hierarchy LIKE concat(mo.hierarchy, '%') AND       ounder.hierarchy LIKE concat('${currentUserHierarchy}', '%') LEFT JOIN m_client mc ON        mc.office_id=ounder.id LEFT JOIN m_loan ml ON        ml.client_id=mc.id AND       ml.loan_status_id=300 LEFT JOIN m_loan_arrears_aging mlaa ON        mlaa.loan_id=ml.id LEFT JOIN m_loan_repayment_schedule mr ON        mr.loan_id=ml.id AND       mr.completed_derived=false LEFT JOIN m_product_loan mp ON        mp.id=ml.product_id LEFT JOIN m_staff ms ON        ms.id=ml.loan_officer_id LEFT JOIN m_currency cu ON        cu.code=ml.currency_code WHERE     mo.id='${officeId}' AND       ( coalesce(ml.loan_officer_id, -10) = ${loanOfficerId} OR        '-1' = ${loanOfficerId}) AND       mr.duedate BETWEEN '${startDate}' AND '${endDate}' ORDER BY  ounder.id, mr.duedate, ml.account_no"/>
            <column name="description" value="Test"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="94"/>
            <column name="report_name" value="Expected Payments By Date - Formatted"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="96"/>
            <column name="report_name" value="GroupSummaryCounts"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category"/>
            <column name="report_sql" value="SELECT    x.* FROM      m_office o,&#10;          m_group g,&#10;          (&#10;                    SELECT    a.activeclients,&#10;                              (b.activeclientloans + c.activegrouploans) AS activeloans,&#10;                              b.activeclientloans,&#10;                              c.activegrouploans,&#10;                              (b.activeclientborrowers + c.activegroupborrowers) AS activeborrowers,&#10;                              b.activeclientborrowers,&#10;                              c.activegroupborrowers,&#10;                              (b.overdueclientloans + c.overduegrouploans) AS overdueloans,&#10;                              b.overdueclientloans,&#10;                              c.overduegrouploans&#10;                    FROM      (&#10;                                     SELECT Count(*) AS activeclients&#10;                                     FROM   m_group topgroup&#10;                                     JOIN   m_group g&#10;                                     ON     g.hierarchy LIKE Concat(topgroup.hierarchy, '%')&#10;                                     JOIN   m_group_client gc&#10;                                     ON     gc.group_id = g.id&#10;                                     JOIN   m_client c&#10;                                     ON     c.id = gc.client_id&#10;                                     WHERE  topgroup.id = ${groupId}&#10;                                     AND    c.status_enum = 300) a,&#10;                              (&#10;                                     SELECT count(*) AS activeclientloans,&#10;                                            count(DISTINCT(l.client_id)) AS activeclientborrowers,&#10;                                            coalesce(sum(&#10;                                            CASE&#10;                                                   WHEN laa.loan_id IS NOT NULL THEN 1&#10;                                                   ELSE 0&#10;                                            end),&#10;                                            0) AS overdueclientloans&#10;                    FROM      m_group topgroup&#10;                    JOIN      m_group g&#10;                    ON        g.hierarchy LIKE concat(topgroup.hierarchy, '%')&#10;                    JOIN      m_loan l&#10;                    ON        l.group_id = g.id&#10;                    AND       l.client_id IS NOT NULL&#10;                    LEFT JOIN m_loan_arrears_aging laa&#10;                    ON        laa.loan_id = l.id&#10;                    WHERE     topgroup.id = ${groupId}&#10;                    AND       l.loan_status_id = 300) b,&#10;          (&#10;                 SELECT count(*)  AS activegrouploans,&#10;                        count(DISTINCT(l.group_id)) AS activegroupborrowers,&#10;                        coalesce(sum(&#10;                        CASE&#10;                               WHEN laa.loan_id IS NOT NULL THEN 1&#10;                               ELSE 0&#10;                        end),&#10;                        0) AS overduegrouploans&#10;FROM      m_group topgroup JOIN      m_group g ON        g.hierarchy LIKE concat(topgroup.hierarchy, '%') JOIN      m_loan l ON        l.group_id = g.id AND       l.client_id IS NULL LEFT JOIN m_loan_arrears_aging laa ON        laa.loan_id = l.id WHERE     topgroup.id = ${groupId} AND       l.loan_status_id = 300) c ) x WHERE g.id = ${groupId} AND o.id = g.office_id AND o.hierarchy LIKE concat('${currentUserHierarchy}', '%')&#10;"/>
            <column name="description" value="Utility query for getting group summary count details for a group_id"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="97"/>
            <column name="report_name" value="GroupSummaryAmounts"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category"/>
            <column name="report_sql" value="&#10;SELECT    Coalesce(cur.display_symbol, l.currency_code)  AS currency,&#10;          Coalesce(Sum(l.principal_disbursed_derived),0) AS totaldisbursedamount,&#10;          Coalesce(Sum(l.principal_outstanding_derived),0) AS totalloanoutstandingamount,&#10;          Count(laa.loan_id) AS overdueloans,&#10;          Coalesce(Sum(laa.total_overdue_derived), 0)  AS totalloanoverdueamount&#10;FROM      m_group topgroup JOIN      m_office o ON        o.id = topgroup.office_id AND       o.hierarchy LIKE Concat('${currentUserHierarchy}', '%') JOIN      m_group g ON        g.hierarchy LIKE Concat(topgroup.hierarchy, '%') JOIN      m_loan l ON        l.group_id = g.id LEFT JOIN m_loan_arrears_aging laa ON        laa.loan_id = l.id LEFT JOIN m_currency cur ON        cur.code = l.currency_code WHERE     topgroup.id = ${groupId} AND       l.disbursedon_date IS NOT NULL GROUP BY  l.currency_code,&#10;          cur.display_symbol&#10;"/>
            <column name="description" value="Utility query for getting group summary currency amount details for a group_id"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="106"/>
            <column name="report_name" value="TxnRunningBalances"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Transaction"/>
            <column name="report_sql" value="select DATE ${startDate} AS &quot;Transaction Date&quot;, 'Opening Balance' AS &quot;Transaction Type&quot;, null AS &quot;Office&quot;, null AS &quot;Loan Officer&quot;, null AS &quot;Loan Account No&quot;, null AS &quot;Loan Product&quot;, null AS &quot;Currency&quot;, null AS &quot;Client Account No&quot;, null AS &quot;Client&quot;, null AS &quot;Principal&quot;, null AS &quot;Interest&quot;, COALESCE(round(sum(CASE WHEN txn.transaction_type_enum  = 1 /* disbursement */ THEN COALESCE(txn.interest_portion_derived, 0.00) ELSE 0 END), 2), 0.00) AS &quot;Outstanding Principal&quot;, COALESCE(round(sum(CASE WHEN txn.transaction_type_enum in (2, 5, 8) /* repayment, repayment at disbursal, recovery repayment */ THEN COALESCE(txn.interest_portion_derived, 0.00) ELSE 0 END), 2), 0.00) AS &quot;Interest Income&quot;, COALESCE(round(sum(CASE WHEN txn.transaction_type_enum = 6 THEN COALESCE(txn.principal_portion_derived, 0.00) ELSE 0 END), 2), 0.00) AS &quot;Principal Write Off&quot; from m_office o join m_office ounder on ounder.hierarchy like concat(o.hierarchy, '%') and ounder.hierarchy like concat('${currentUserHierarchy}', '%') join m_client c on c.office_id = ounder.id join m_loan l on l.client_id = c.id join m_product_loan lp on lp.id = l.product_id join m_loan_transaction txn on txn.loan_id = l.id left join m_currency cur on cur.code = l.currency_code where txn.is_reversed = false and txn.transaction_type_enum not in (10, 11) and o.id = '${officeId}' and txn.transaction_date &lt; DATE ${startDate} union all select txn.transaction_date AS &quot;Transaction Date&quot;, cast(COALESCE(re.enum_message_property, concat('Unknown Transaction Type Value:', ' ', txn.transaction_type_enum)) as char) AS &quot;Transaction Type&quot;, ounder.name AS &quot;Office&quot;, lo.display_name AS &quot;Loan Officer&quot;, l.account_no AS &quot;Loan Account No&quot;, lp.name AS &quot;Loan Product&quot;, COALESCE(cur.display_symbol, l.currency_code) AS &quot;Currency&quot;, c.account_no AS &quot;Client Account No&quot;, c.display_name AS &quot;Client&quot;, COALESCE(txn.principal_portion_derived, 0.00) AS &quot;Principal&quot;, COALESCE(txn.interest_portion_derived, 0.00) AS &quot;Interest&quot;, COALESCE(round(sum(CASE WHEN txn.transaction_type_enum = 1 /* disbursement */ THEN COALESCE(txn.amount, 0.00) ELSE -1 * COALESCE(txn.principal_portion_derived, 0.00) END), 2), 0.00) AS &quot;Outstanding Principal&quot;, COALESCE(round(sum(CASE WHEN txn.transaction_type_enum in (2, 5, 8) /* repayment, repayment at disbursal, recovery repayment */ THEN COALESCE(txn.interest_portion_derived, 0.00) ELSE 0 END), 2), 0.00) AS &quot;Interest Income&quot;, COALESCE(round(sum(CASE WHEN txn.transaction_type_enum = 6 THEN COALESCE(txn.principal_portion_derived, 0.00) ELSE 0 END), 2), 0.00) AS &quot;Principal Write Off&quot; from m_office o join m_office ounder on ounder.hierarchy like concat(o.hierarchy, '%') and ounder.hierarchy like concat('${currentUserHierarchy}', '%') join m_client c on c.office_id = ounder.id join m_loan l on l.client_id = c.id left join m_staff lo on lo.id = l.loan_officer_id join m_product_loan lp on lp.id = l.product_id join m_loan_transaction txn on txn.loan_id = l.id left join m_currency cur on cur.code = l.currency_code left join r_enum_value re on re.enum_name = 'transaction_type_enum' AND re.enum_id = txn.transaction_type_enum where txn.is_reversed = false and txn.transaction_type_enum not in (10, 11) and (COALESCE(l.loan_officer_id, -10) = 9 or '-1' = 9) and o.id = '${officeId}' and txn.transaction_date &gt;= DATE ${startDate} and txn.transaction_date &lt;= DATE ${endDate} group by txn.id, ounder.id, lo.id, l.id, lp.id, cur.id, c.id, re.enum_message_property"/>
            <column name="description" value="Running Balance Txn report for Individual Lending.&#10;Suitable for small MFI's.  Larger could use it using the branch or other parameters.&#10;Basically, suck it and see if its quick enough for you out-of-te box or whether it needs performance work in your situation.&#10;"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="107"/>
            <column name="report_name" value="FieldAgentStats"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Quipo"/>
            <column name="report_sql" value="select COALESCE(cur.display_symbol, l.currency_code) as Currency, /*This query will return more than one entry if more than one currency is used */ count(distinct(c.id)) as activeClients, count(*) as activeLoans, sum(l.principal_disbursed_derived) as disbursedAmount, sum(l.principal_outstanding_derived) as loanOutstandingAmount, round((sum(l.principal_outstanding_derived) * 100) / sum(l.principal_disbursed_derived),2) as loanOutstandingPC, sum(COALESCE(lpa.principal_in_advance_derived,0.0)) as LoanPaidInAdvance, sum( CASE WHEN (CURRENT_DATE - 28 * INTERVAL '1 day') &gt; COALESCE(laa.overdue_since_date_derived, CURRENT_DATE) THEN l.principal_outstanding_derived ELSE 0 END) as portfolioAtRisk, round((sum( CASE WHEN (CURRENT_DATE - 28 * INTERVAL '1 day') &gt; COALESCE(laa.overdue_since_date_derived, CURRENT_DATE) THEN l.principal_outstanding_derived ELSE 0 END) * 100) / sum(l.principal_outstanding_derived), 2) as portfolioAtRiskPC, count(distinct( CASE WHEN (CURRENT_DATE - 28 * INTERVAL '1 day') &gt; COALESCE(laa.overdue_since_date_derived, CURRENT_DATE) THEN c.id ELSE null END)) as clientsInDefault, round((count(distinct( CASE WHEN (CURRENT_DATE - 28 * INTERVAL '1 day') &gt; COALESCE(laa.overdue_since_date_derived, CURRENT_DATE) THEN c.id ELSE null END))) * 100 / count(distinct(c.id)),2) as clientsInDefaultPC, sum(l.principal_disbursed_derived) / count(*) as averageLoanAmount from m_staff fa join m_office o on o.id = fa.office_id AND o.hierarchy like concat('${currentUserHierarchy}', '%') join m_group pgm on pgm.staff_id = fa.id join m_loan l on l.group_id = pgm.id and l.client_id is not null left join m_currency cur on cur.code = l.currency_code left join m_loan_arrears_aging laa on laa.loan_id = l.id left join m_loan_paid_in_advance lpa on lpa.loan_id = l.id join m_client c on c.id = l.client_id where fa.id = ${staffId} and l.loan_status_id = 300 group by l.currency_code, cur.id"/>
            <column name="description" value="Field Agent Statistics"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="108"/>
            <column name="report_name" value="FieldAgentPrograms"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Quipo"/>
            <column name="report_sql" value="select pgm.id, pgm.display_name as name, sts.enum_message_property as status  from m_group pgm  join m_office o on o.id = pgm.office_id AND o.hierarchy like concat('${currentUserHierarchy}', '%') left join r_enum_value sts on sts.enum_name = 'status_enum' and sts.enum_id = pgm.status_enum  where pgm.staff_id = ${staffId}"/>
            <column name="description" value="List of Field Agent Programs"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="109"/>
            <column name="report_name" value="ProgramDetails"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Quipo"/>
            <column name="report_sql" value="select l.id as loanId, l.account_no as loanAccountNo, c.id as clientId, c.account_no as clientAccountNo, pgm.display_name as programName,  (select count(*) from m_loan cy where cy.group_id = pgm.id and cy.client_id =c.id and cy.disbursedon_date &lt;= l.disbursedon_date) as loanCycleNo,  c.display_name as clientDisplayName, COALESCE(cur.display_symbol, l.currency_code) as Currency, COALESCE(l.principal_repaid_derived,0.0) as loanRepaidAmount, COALESCE(l.principal_outstanding_derived, 0.0) as loanOutstandingAmount, COALESCE(lpa.principal_in_advance_derived,0.0) as LoanPaidInAdvance,  COALESCE(laa.principal_overdue_derived, 0.0) as loanInArrearsAmount, CASE WHEN COALESCE(laa.principal_overdue_derived, 0.00) &gt; 0 THEN 'Yes' ELSE 'No' END as inDefault,  CASE WHEN (CURRENT_DATE - 28 * INTERVAL '1 day') &gt; COALESCE(laa.overdue_since_date_derived, CURRENT_DATE) THEN l.principal_outstanding_derived ELSE 0 END as portfolioAtRisk   from m_group pgm  join m_office o on o.id = pgm.office_id AND o.hierarchy like concat('${currentUserHierarchy}', '%')  join m_loan l on l.group_id = pgm.id and l.client_id is not null  left join m_currency cur on cur.code = l.currency_code  join m_client c on c.id = l.client_id left join m_loan_arrears_aging laa on laa.loan_id = l.id  left join m_loan_paid_in_advance lpa on lpa.loan_id = l.id  where pgm.id = ${programId}  and l.loan_status_id = 300 order by c.display_name, l.account_no"/>
            <column name="description" value="List of Loans in a Program"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="110"/>
            <column name="report_name" value="ChildrenStaffList"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Quipo"/>
            <column name="report_sql" value="select s.id, s.display_name, s.firstname, s.lastname, s.organisational_role_enum,&#10;s.organisational_role_parent_staff_id,&#10;sp.display_name AS &quot;organisational_role_parent_staff_display_name&quot;&#10;from m_staff s&#10;join m_staff sp on s.organisational_role_parent_staff_id = sp.id&#10;where s.organisational_role_parent_staff_id = ${staffId}"/>
            <column name="description" value="Get Next Level Down Staff"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="111"/>
            <column name="report_name" value="CoordinatorStats"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Quipo"/>
            <column name="report_sql" value="select COALESCE(cur.display_symbol, l.currency_code) as Currency, /*This query will return more than one entry if more than one currency is used */ count(distinct(c.id)) as activeClients, count(*) as activeLoans, sum(l.principal_disbursed_derived) as disbursedAmount, sum(l.principal_outstanding_derived) as loanOutstandingAmount, round((sum(l.principal_outstanding_derived) * 100) / sum(l.principal_disbursed_derived),2) as loanOutstandingPC, sum(COALESCE(lpa.principal_in_advance_derived,0.0)) as LoanPaidInAdvance, sum( CASE WHEN (CURRENT_DATE - 28 * INTERVAL '1 day') &gt; COALESCE(laa.overdue_since_date_derived, CURRENT_DATE) THEN l.principal_outstanding_derived ELSE 0 END) as portfolioAtRisk, round((sum( CASE WHEN (CURRENT_DATE - 28 * INTERVAL '1 day') &gt; COALESCE(laa.overdue_since_date_derived, CURRENT_DATE) THEN l.principal_outstanding_derived ELSE 0 END) * 100) / sum(l.principal_outstanding_derived), 2) as portfolioAtRiskPC, count(distinct( CASE WHEN (CURRENT_DATE - 28 * INTERVAL '1 day') &gt; COALESCE(laa.overdue_since_date_derived, CURRENT_DATE) THEN c.id ELSE null END)) as clientsInDefault, round((count(distinct( CASE WHEN (CURRENT_DATE - 28 * INTERVAL '1 day') &gt; COALESCE(laa.overdue_since_date_derived, CURRENT_DATE) THEN c.id ELSE null END))) * 100 / count(distinct(c.id)),2) as clientsInDefaultPC, sum(l.principal_disbursed_derived) / count(*) as averageLoanAmount from m_staff coord join m_staff fa on fa.organisational_role_parent_staff_id = coord.id join m_office o on o.id = fa.office_id AND o.hierarchy like concat('${currentUserHierarchy}', '%') join m_group pgm on pgm.staff_id = fa.id join m_loan l on l.group_id = pgm.id and l.client_id is not null left join m_currency cur on cur.code = l.currency_code left join m_loan_arrears_aging laa on laa.loan_id = l.id left join m_loan_paid_in_advance lpa on lpa.loan_id = l.id join m_client c on c.id = l.client_id where coord.id = ${staffId} and l.loan_status_id = 300 group by l.currency_code, cur.id"/>
            <column name="description" value="Coordinator Statistics"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="112"/>
            <column name="report_name" value="BranchManagerStats"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Quipo"/>
            <column name="report_sql" value="SELECT    Coalesce(cur.display_symbol, l.currency_code)  AS currency, Count(DISTINCT(c.id))  AS activeclients, Count(*) AS activeloans, Sum(l.principal_disbursed_derived) AS disbursedamount, Sum(l.principal_outstanding_derived) AS loanoutstandingamount, Round((Sum(l.principal_outstanding_derived) * 100) / Sum(l.principal_disbursed_derived),2) AS loanoutstandingpc, Sum(Coalesce(lpa.principal_in_advance_derived,0.0))  AS loanpaidinadvance, sum( CASE WHEN ( CURRENT_DATE - 28 * INTERVAL '1 day') &gt; coalesce(laa.overdue_since_date_derived, CURRENT_DATE) THEN l.principal_outstanding_derived ELSE 0 end) AS portfolioatrisk, round((sum( CASE WHEN ( CURRENT_DATE - 28 * INTERVAL '1 day' ) &gt; coalesce(laa.overdue_since_date_derived, CURRENT_DATE) THEN l.principal_outstanding_derived ELSE 0 end) * 100) / sum(l.principal_outstanding_derived), 2) AS portfolioatriskpc, count(DISTINCT( CASE WHEN ( CURRENT_DATE - 28 * INTERVAL '1 day' ) &gt; coalesce(laa.overdue_since_date_derived, CURRENT_DATE) THEN c.id ELSE NULL end)) AS clientsindefault, round((count(DISTINCT( CASE WHEN ( CURRENT_DATE - 28 * INTERVAL '1 day' ) &gt; coalesce(laa.overdue_since_date_derived, CURRENT_DATE) THEN c.id ELSE NULL end)) * 100) / count(DISTINCT(c.id)), 2)  AS clientsindefaultpc, (sum(l.principal_disbursed_derived) / count(*)) AS averageloanamount FROM      m_staff bm JOIN      m_staff coord ON        coord.organisational_role_parent_staff_id = bm.id JOIN      m_staff fa ON        fa.organisational_role_parent_staff_id = coord.id JOIN      m_office o ON        o.id = fa.office_id AND       o.hierarchy LIKE concat('${currentUserHierarchy}', '%') JOIN      m_group pgm ON        pgm.staff_id = fa.id JOIN      m_loan l ON        l.group_id = pgm.id AND       l.client_id IS NOT NULL LEFT JOIN m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_loan_arrears_aging laa ON        laa.loan_id = l.id LEFT JOIN m_loan_paid_in_advance lpa ON        lpa.loan_id = l.id JOIN      m_client c ON        c.id = l.client_id WHERE     bm.id = ${staffId} AND       l.loan_status_id = 300 GROUP BY  l.currency_code, cur.display_symbol"/>
            <column name="description" value="Branch Manager Statistics"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="113"/>
            <column name="report_name" value="ProgramDirectorStats"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Quipo"/>
            <column name="report_sql" value="SELECT    Coalesce(cur.display_symbol, l.currency_code) AS currency, /*This query will return more than one entry if more than one currency is used */ Count(DISTINCT(c.id))  AS activeclients, Count(*) AS activeloans, Sum(l.principal_disbursed_derived) AS disbursedamount, Sum(l.principal_outstanding_derived) AS loanoutstandingamount, Round((Sum(l.principal_outstanding_derived) * 100) / Sum(l.principal_disbursed_derived),2) AS loanoutstandingpc, Sum(Coalesce(lpa.principal_in_advance_derived,0.0))  AS loanpaidinadvance, sum( CASE WHEN ( CURRENT_DATE - 28 * INTERVAL '1 day') &gt; coalesce(laa.overdue_since_date_derived, CURRENT_DATE) THEN l.principal_outstanding_derived ELSE 0 end) AS portfolioatrisk, round((sum( CASE WHEN ( CURRENT_DATE - 28 * INTERVAL '1 day' ) &gt; coalesce(laa.overdue_since_date_derived, CURRENT_DATE) THEN l.principal_outstanding_derived ELSE 0 end) * 100) / sum(l.principal_outstanding_derived), 2) AS portfolioatriskpc, count(DISTINCT( CASE WHEN ( CURRENT_DATE - 28 * INTERVAL '1 day' ) &gt; coalesce(laa.overdue_since_date_derived, CURRENT_DATE) THEN c.id ELSE NULL end)) AS clientsindefault, round((count(DISTINCT( CASE WHEN ( CURRENT_DATE - 28 * INTERVAL '1 day' ) &gt; coalesce(laa.overdue_since_date_derived, CURRENT_DATE) THEN c.id ELSE NULL end)) * 100) / count(DISTINCT(c.id)), 2)  AS clientsindefaultpc, (sum(l.principal_disbursed_derived) / count(*)) AS averageloanamount FROM      m_staff pd JOIN      m_staff bm ON        bm.organisational_role_parent_staff_id = pd.id JOIN      m_staff coord ON        coord.organisational_role_parent_staff_id = bm.id JOIN      m_staff fa ON        fa.organisational_role_parent_staff_id = coord.id JOIN      m_office o ON        o.id = fa.office_id AND       o.hierarchy LIKE concat('${currentUserHierarchy}', '%') JOIN      m_group pgm ON        pgm.staff_id = fa.id JOIN      m_loan l ON        l.group_id = pgm.id AND       l.client_id IS NOT NULL LEFT JOIN m_currency cur ON        cur.code = l.currency_code LEFT JOIN m_loan_arrears_aging laa ON        laa.loan_id = l.id LEFT JOIN m_loan_paid_in_advance lpa ON        lpa.loan_id = l.id JOIN      m_client c ON        c.id = l.client_id WHERE     pd.id = ${staffId} AND       l.loan_status_id = 300 GROUP BY  l.currency_code, cur.display_symbol"/>
            <column name="description" value="Program DirectorStatistics"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="114"/>
            <column name="report_name" value="ProgramStats"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Quipo"/>
            <column name="report_sql" value="select COALESCE(cur.display_symbol, l.currency_code) as Currency, /*This query will return more than one entry if more than one currency is used */ count(distinct(c.id)) as activeClients, count(*) as activeLoans, sum(l.principal_disbursed_derived) as disbursedAmount, sum(l.principal_outstanding_derived) as loanOutstandingAmount, round((sum(l.principal_outstanding_derived) * 100) / sum(l.principal_disbursed_derived),2) as loanOutstandingPC, sum(COALESCE(lpa.principal_in_advance_derived,0.0)) as LoanPaidInAdvance, sum( CASE WHEN (CURRENT_DATE - 28 * INTERVAL '1 day') &gt; COALESCE(laa.overdue_since_date_derived, CURRENT_DATE) THEN l.principal_outstanding_derived ELSE 0 END) as portfolioAtRisk, round((sum( CASE WHEN (CURRENT_DATE - 28 * INTERVAL '1 day') &gt; COALESCE(laa.overdue_since_date_derived, CURRENT_DATE) THEN l.principal_outstanding_derived ELSE 0 END) * 100) / sum(l.principal_outstanding_derived), 2) as portfolioAtRiskPC, count(distinct( CASE WHEN (CURRENT_DATE - 28 * INTERVAL '1 day') &gt; COALESCE(laa.overdue_since_date_derived, CURRENT_DATE) THEN c.id ELSE null END)) as clientsInDefault, round((count(distinct( CASE WHEN (CURRENT_DATE - 28 * INTERVAL '1 day') &gt; COALESCE(laa.overdue_since_date_derived, CURRENT_DATE) THEN c.id ELSE null END))) * 100 / count(distinct(c.id)),2) as clientsInDefaultPC, sum(l.principal_disbursed_derived) / count(*) as averageLoanAmount from m_group pgm join m_office o on o.id = pgm.office_id AND o.hierarchy like concat('${currentUserHierarchy}', '%') join m_loan l on l.group_id = pgm.id and l.client_id is not null left join m_currency cur on cur.code = l.currency_code left join m_loan_arrears_aging laa on laa.loan_id = l.id left join m_loan_paid_in_advance lpa on lpa.loan_id = l.id join m_client c on c.id = l.client_id where pgm.id = ${programId} and l.loan_status_id = 300 group  by l.currency_code, cur.id"/>
            <column name="description" value="Program Statistics"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="115"/>
            <column name="report_name" value="ClientSummary "/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category"/>
            <column name="report_sql" value="SELECT x.* FROM m_client c, m_office o, (SELECT a.loanCycle, a.activeLoans, b.lastLoanAmount, d.activeSavings, d.totalSavings FROM (SELECT COALESCE(MAX(l.loan_counter),0) AS loanCycle, COUNT(l.id) AS activeLoans FROM m_loan l WHERE l.loan_status_id=300 AND l.client_id=${clientId}) a, (SELECT count(l.id), COALESCE(l.principal_amount,0) AS lastLoanAmount FROM m_loan l WHERE l.client_id=${clientId} AND l.disbursedon_date = (SELECT COALESCE(MAX(disbursedon_date),NOW()) FROM m_loan where client_id=${clientId} and loan_status_id=300) group by l.principal_amount) b, (SELECT COUNT(s.id) AS activeSavings, COALESCE(SUM(s.account_balance_derived),0) AS totalSavings FROM m_savings_account s WHERE s.status_enum=300 AND s.client_id=${clientId}) d) x WHERE c.id=${clientId} AND o.id = c.office_id AND o.hierarchy LIKE CONCAT('${currentUserHierarchy}', '%')"/>
            <column name="description" value="Utility query for getting the client summary details"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="116"/>
            <column name="report_name" value="LoanCyclePerProduct"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category"/>
            <column name="report_sql" value="SELECT lp.name AS &quot;productName&quot;, MAX(l.loan_product_counter) AS &quot;loanProductCycle&quot; FROM m_loan l JOIN m_product_loan lp ON l.product_id=lp.id WHERE lp.include_in_borrower_cycle=true AND l.loan_product_counter IS NOT NULL AND l.client_id=${clientId} GROUP BY lp.id"/>
            <column name="description" value="Utility query for getting the client loan cycle details"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="117"/>
            <column name="report_name" value="GroupSavingSummary"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category"/>
            <column name="report_sql" value="select COALESCE(cur.display_symbol, sa.currency_code) as currency, count(sa.id) as totalSavingAccounts, COALESCE(sum(sa.account_balance_derived),0) as totalSavings from m_group topgroup join m_office o on o.id = topgroup.office_id and o.hierarchy like concat('${currentUserHierarchy}', '%') join m_group g on g.hierarchy like concat(topgroup.hierarchy, '%') join m_savings_account sa on sa.group_id = g.id left join m_currency cur on cur.code = sa.currency_code where topgroup.id = ${groupId} and sa.activatedon_date is not null group by sa.currency_code, cur.id"/>
            <column name="description" value="Utility query for getting group or center saving summary details for a group_id"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="118"/>
            <column name="report_name" value="Savings Transactions"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Savings"/>
            <column name="report_sql"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="119"/>
            <column name="report_name" value="Client Savings Summary"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Savings"/>
            <column name="report_sql"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="120"/>
            <column name="report_name" value="Active Loans - Details(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="121"/>
            <column name="report_name" value="Active Loans - Summary(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="122"/>
            <column name="report_name" value="Active Loans by Disbursal Period(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="123"/>
            <column name="report_name" value="Active Loans in last installment Summary(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="124"/>
            <column name="report_name" value="Active Loans in last installment(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="125"/>
            <column name="report_name" value="Active Loans Passed Final Maturity Summary(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="126"/>
            <column name="report_name" value="Active Loans Passed Final Maturity(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="127"/>
            <column name="report_name" value="Aging Detail(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="128"/>
            <column name="report_name" value="Aging Summary (Arrears in Months)(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="129"/>
            <column name="report_name" value="Aging Summary (Arrears in Weeks)(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="130"/>
            <column name="report_name" value="Client Listing(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Client"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="131"/>
            <column name="report_name" value="Client Loans Listing(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Client"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="132"/>
            <column name="report_name" value="Expected Payments By Date - Basic(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="133"/>
            <column name="report_name" value="Funds Disbursed Between Dates Summary by Office(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Fund"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="134"/>
            <column name="report_name" value="Funds Disbursed Between Dates Summary(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Fund"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="135"/>
            <column name="report_name" value="Loans Awaiting Disbursal Summary by Month(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="136"/>
            <column name="report_name" value="Loans Awaiting Disbursal Summary(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="137"/>
            <column name="report_name" value="Loans Awaiting Disbursal(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="138"/>
            <column name="report_name" value="Loans Pending Approval(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="139"/>
            <column name="report_name" value="Obligation Met Loans Details(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="140"/>
            <column name="report_name" value="Obligation Met Loans Summary(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="141"/>
            <column name="report_name" value="Portfolio at Risk by Branch(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="142"/>
            <column name="report_name" value="Portfolio at Risk(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="143"/>
            <column name="report_name" value="Rescheduled Loans(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="144"/>
            <column name="report_name" value="TxnRunningBalances(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Transaction"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="145"/>
            <column name="report_name" value="Written-Off Loans(Pentaho)"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="146"/>
            <column name="report_name" value="Client Saving Transactions"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Savings"/>
            <column name="report_sql"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="147"/>
            <column name="report_name" value="Client Loan Account Schedule"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="148"/>
            <column name="report_name" value="GroupNamesByStaff"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype" value=""/>
            <column name="report_category" value=""/>
            <column name="report_sql" value="SELECT gr.id AS id, gr.display_name AS name FROM   m_group gr WHERE  gr.level_id=1 AND    gr.staff_id = ${staffId}"/>
            <column name="description" value=""/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="149"/>
            <column name="report_name" value="ClientTrendsByDay"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype" value=""/>
            <column name="report_category" value="Client"/>
            <column name="report_sql" value="SELECT COUNT(cl.id) AS count, cl.activation_date AS days FROM m_office of LEFT JOIN m_client cl on of.id = cl.office_id WHERE of.hierarchy like concat((select ino.hierarchy from m_office ino where ino.id = '${officeId}'),'%' ) AND (cl.activation_date BETWEEN (CURRENT_DATE - 12 * INTERVAL '1 day') AND NOW()) GROUP BY days"/>
            <column name="description" value="Retrieves the number of clients joined in last 12 days"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="150"/>
            <column name="report_name" value="ClientTrendsByWeek"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype" value=""/>
            <column name="report_category" value="Client"/>
            <column name="report_sql" value="SELECT COUNT(cl.id) AS count, EXTRACT(WEEK FROM cl.activation_date) AS Weeks FROM m_office of LEFT JOIN m_client cl on of.id = cl.office_id WHERE of.hierarchy like concat((select ino.hierarchy from m_office ino where ino.id = '${officeId}'),'%' ) AND (cl.activation_date BETWEEN (CURRENT_DATE - 12 * INTERVAL '1 WEEK') AND NOW()) GROUP BY Weeks"/>
            <column name="description" value=""/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="151"/>
            <column name="report_name" value="ClientTrendsByMonth"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype" value=""/>
            <column name="report_category" value="Client"/>
            <column name="report_sql" value="SELECT    Count(cl.id)  AS count, to_char(cl.activation_date, 'Month') AS &quot;Months&quot; FROM      m_office of LEFT JOIN m_client cl ON        of.id = cl.office_id WHERE     of.hierarchy LIKE Concat( ( SELECT ino.hierarchy FROM   m_office ino WHERE  ino.id = '${officeId}'),'%' ) AND       ( cl.activation_date BETWEEN (CURRENT_DATE - 12 * INTERVAL '1 MONTH') AND       DATE_TRUNC('day', now())) GROUP BY  &quot;Months&quot;"/>
            <column name="description" value=""/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="152"/>
            <column name="report_name" value="LoanTrendsByDay"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype" value=""/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT COUNT(ln.id) AS lcount, ln.disbursedon_date AS days FROM m_office of LEFT JOIN m_client cl on of.id = cl.office_id LEFT JOIN m_loan ln on cl.id = ln.client_id WHERE of.hierarchy like concat((select ino.hierarchy from m_office ino where ino.id = '${officeId}'),'%' ) AND (ln.disbursedon_date BETWEEN (CURRENT_DATE - 12 * INTERVAL '1 day') AND NOW()) GROUP BY days"/>
            <column name="description" value="Retrieves Number of loans disbursed for last 12 days"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="153"/>
            <column name="report_name" value="LoanTrendsByWeek"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype" value=""/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT COUNT(ln.id) AS lcount, EXTRACT(WEEK FROM ln.disbursedon_date) AS Weeks FROM m_office of LEFT JOIN m_client cl on of.id = cl.office_id LEFT JOIN m_loan ln on cl.id = ln.client_id WHERE of.hierarchy like concat((select ino.hierarchy from m_office ino where ino.id = '${officeId}'),'%' ) AND (ln.disbursedon_date BETWEEN (CURRENT_DATE - 12 * INTERVAL '1 week') AND NOW()) GROUP BY Weeks"/>
            <column name="description" value=""/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="154"/>
            <column name="report_name" value="LoanTrendsByMonth"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype" value=""/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT COUNT(ln.id) AS lcount, EXTRACT(MONTH FROM ln.disbursedon_date) AS &quot;Months&quot; FROM m_office of LEFT JOIN m_client cl on of.id = cl.office_id LEFT JOIN m_loan ln on cl.id = ln.client_id WHERE of.hierarchy like concat((select ino.hierarchy from m_office ino where ino.id = '${officeId}'),'%' ) AND (ln.disbursedon_date BETWEEN (CURRENT_DATE - 12 * INTERVAL '1 month') AND NOW()) GROUP BY &quot;Months&quot;"/>
            <column name="description" value=""/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="155"/>
            <column name="report_name" value="Demand_Vs_Collection"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype" value=""/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="select amount.AmountDue-amount.AmountPaid as AmountDue, amount.AmountPaid as AmountPaid from (SELECT (COALESCE(SUM(ls.principal_amount),0) - COALESCE(SUM(ls.principal_writtenoff_derived),0)  + COALESCE(SUM(ls.interest_amount),0) - COALESCE(SUM(ls.interest_writtenoff_derived),0)   - COALESCE(SUM(ls.interest_waived_derived),0) + COALESCE(SUM(ls.fee_charges_amount),0) - COALESCE(SUM(ls.fee_charges_writtenoff_derived),0) - COALESCE(SUM(ls.fee_charges_waived_derived),0)  + COALESCE(SUM(ls.penalty_charges_amount),0) - COALESCE(SUM(ls.penalty_charges_writtenoff_derived),0)   - COALESCE(SUM(ls.penalty_charges_waived_derived),0) ) AS AmountDue, (COALESCE(SUM(ls.principal_completed_derived),0) - COALESCE(SUM(ls.principal_writtenoff_derived),0) + COALESCE(SUM(ls.interest_completed_derived),0) - COALESCE(SUM(ls.interest_writtenoff_derived),0)   - COALESCE(SUM(ls.interest_waived_derived),0) + COALESCE(SUM(ls.fee_charges_completed_derived),0) - COALESCE(SUM(ls.fee_charges_writtenoff_derived),0) - COALESCE(SUM(ls.fee_charges_waived_derived),0)  + COALESCE(SUM(ls.penalty_charges_completed_derived),0) - COALESCE(SUM(ls.penalty_charges_writtenoff_derived),0)   - COALESCE(SUM(ls.penalty_charges_waived_derived),0) ) AS AmountPaid FROM m_office of LEFT JOIN m_client cl ON of.id = cl.office_id LEFT JOIN m_loan ln ON cl.id = ln.client_id LEFT JOIN m_loan_repayment_schedule ls ON ln.id = ls.loan_id WHERE ls.duedate = DATE_TRUNC('day', NOW()) AND (of.hierarchy LIKE CONCAT(( SELECT ino.hierarchy FROM m_office ino WHERE ino.id = '${officeId}'),'%'))) as amount"/>
            <column name="description" value="Demand Vs Collection"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="156"/>
            <column name="report_name" value="Disbursal_Vs_Awaitingdisbursal"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype" value=""/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT awaitinddisbursal.amount-disbursedAmount.amount as amountToBeDisburse, disbursedAmount.amount as disbursedAmount FROM (SELECT COUNT(ln.id) AS noOfLoans, COALESCE(SUM(ln.principal_amount),0) AS amount FROM m_office of LEFT JOIN m_client cl ON cl.office_id = of.id LEFT JOIN m_loan ln ON cl.id = ln.client_id WHERE ln.expected_disbursedon_date = DATE_TRUNC('day', NOW()) AND (ln.loan_status_id=200 OR ln.loan_status_id=300) AND of.hierarchy like concat((SELECT ino.hierarchy FROM m_office ino WHERE ino.id = '${officeId}'),'%' )) awaitinddisbursal, (SELECT COUNT(ltrxn.id) as count, COALESCE(SUM(ltrxn.amount),0) as amount FROM m_office of LEFT JOIN m_client cl ON cl.office_id = of.id LEFT JOIN m_loan ln ON cl.id = ln.client_id LEFT JOIN m_loan_transaction ltrxn ON ln.id = ltrxn.loan_id WHERE ltrxn.transaction_date = DATE_TRUNC('day', NOW()) AND ltrxn.is_reversed = false AND ltrxn.transaction_type_enum=1 AND of.hierarchy like concat((select ino.hierarchy from m_office ino where ino.id = '${officeId}'),'%' )) disbursedAmount"/>
            <column name="description" value="Disbursal_Vs_Awaitingdisbursal"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="157"/>
            <column name="report_name" value="Savings Transaction Receipt"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category"/>
            <column name="report_sql"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="158"/>
            <column name="report_name" value="Loan Transaction Receipt"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category"/>
            <column name="report_sql"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="159"/>
            <column name="report_name" value="Staff Assignment History"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category"/>
            <column name="report_sql"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="160"/>
            <column name="report_name" value="GeneralLedgerReport"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Accounting"/>
            <column name="report_sql"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="161"/>
            <column name="report_name" value="Active Loan Summary per Branch"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="162"/>
            <column name="report_name" value="Balance Outstanding"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="163"/>
            <column name="report_name" value="Collection Report"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="164"/>
            <column name="report_name" value="Disbursal Report"/>
            <column name="report_type" value="Pentaho"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="165"/>
            <column name="report_name" value="Savings Accounts Dormancy Report"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="Savings"/>
            <column name="report_sql" value="select cl.display_name AS &quot;Client Display Name&quot;, sa.account_no AS &quot;Account Number&quot;, cl.mobile_no AS &quot;Mobile Number&quot;, (select COALESCE(max(sat.transaction_date), sa.activatedon_date) from m_savings_account_transaction as sat where sat.is_reversed = false and sat.transaction_type_enum in (1, 2) and sat.savings_account_id = sa.id) AS &quot;Date of Last Activity&quot;, EXTRACT(DAY FROM (CURRENT_DATE - (select COALESCE(max(sat.transaction_date), sa.activatedon_date) from m_savings_account_transaction as sat where sat.is_reversed = false and sat.transaction_type_enum in (1, 2) and sat.savings_account_id = sa.id)::TIMESTAMP)) AS &quot;Days Since Last Activity&quot; from m_savings_account as sa inner join m_savings_product as sp on (sa.product_id = sp.id and sp.is_dormancy_tracking_active = true) left join m_client as cl on sa.client_id = cl.id where sa.sub_status_enum = ${subStatus} and cl.office_id = '${officeId}'"/>
            <column name="description"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="166"/>
            <column name="report_name" value="Active Clients"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="NonTriggered"/>
            <column name="report_category" value="Client"/>
            <column name="report_sql" value="SELECT c.id AS id, c.firstname AS firstname, c.middlename AS middlename, c.lastname  AS lastname, c.display_name  AS fullname, c.mobile_no AS mobileno, Concat(REPEAT('..', ((Length(ounder.hierarchy) - Length( REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS officename, o.id AS officenumber FROM  m_office o JOIN  m_office ounder ON ounder.hierarchy LIKE Concat(o.hierarchy, '%') JOIN  m_client c ON c.office_id = ounder.id LEFT JOIN r_enum_value r ON r.enum_name = 'status_enum' AND r.enum_id = c.status_enum WHERE o.id = '${officeId}' AND c.status_enum = 300 AND (coalesce(c.staff_id, -10) = ${loanOfficerId} OR'-1' = ${loanOfficerId}) GROUP BY c.id, ounder.hierarchy, ounder.name, o.id ORDER BY ounder.hierarchy, c.account_no"/>
            <column name="description" value="All clients with the status ‘Active’"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="167"/>
            <column name="report_name" value="Prospective Clients"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="NonTriggered"/>
            <column name="report_category" value="Client"/>
            <column name="report_sql" value="SELECT c.id AS id, c.firstname AS firstName, c.middlename AS middleName, c.lastname AS lastName, c.display_name AS fullName, c.mobile_no AS mobileNo, CONCAT(REPEAT('..', ((LENGTH(ounder.hierarchy) - LENGTH(REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS officeName, o.id AS officeNumber FROM m_office o JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(o.hierarchy, '%') JOIN m_client c ON c.office_id = ounder.id LEFT JOIN r_enum_value r ON r.enum_name = 'status_enum' AND r.enum_id = c.status_enum LEFT JOIN m_loan l ON l.client_id = c.id WHERE o.id = '${officeId}' AND c.status_enum = 300 AND (COALESCE(c.staff_id, -10) = ${loanOfficerId} OR '-1' = ${loanOfficerId}) AND l.client_id IS NULL GROUP BY c.id, ounder.id, o.id ORDER BY ounder.hierarchy, c.account_no"/>
            <column name="description" value="All clients with the status ‘Active’ who have never had a loan before"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="168"/>
            <column name="report_name" value="Active Loan Clients"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="NonTriggered"/>
            <column name="report_category" value="Client"/>
            <column name="report_sql" value="SELECT c.id AS id, c.firstname AS firstName, c.middlename AS middleName, c.lastname AS lastName, c.display_name AS fullName, c.mobile_no AS mobileNo, l.principal_amount AS loanAmount, (COALESCE(l.principal_outstanding_derived, 0) + COALESCE(l.interest_outstanding_derived, 0) + COALESCE(l.fee_charges_outstanding_derived, 0) + COALESCE(l.penalty_charges_outstanding_derived, 0)) AS loanOutstanding, l.principal_disbursed_derived AS loanDisbursed, ounder.id AS officeNumber, l.account_no AS loanAccountId, gua.lastname AS guarantorLastName, COUNT(gua.id) AS numberOfGuarantors, g.display_name AS groupName FROM m_office o JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(o.hierarchy, '%') JOIN m_client c ON c.office_id = ounder.id JOIN m_loan l ON l.client_id = c.id JOIN m_product_loan pl ON pl.id = l.product_id LEFT JOIN m_group_client gc ON gc.client_id = c.id LEFT JOIN m_group g ON g.id = gc.group_id LEFT JOIN m_staff lo ON lo.id = l.loan_officer_id LEFT JOIN m_currency cur ON cur.code = l.currency_code LEFT JOIN m_guarantor gua ON gua.loan_id = l.id WHERE o.id = '${officeId}' AND (COALESCE(l.loan_officer_id, -10) = ${loanOfficerId} OR '-1' = ${loanOfficerId}) AND l.loan_status_id = 300 AND (EXTRACT(DAY FROM (CURRENT_DATE - l.disbursedon_date::TIMESTAMP)) BETWEEN ${cycleX} AND ${cycleY}) GROUP BY l.id, c.id, ounder.id, gua.id, g.id ORDER BY ounder.hierarchy, l.currency_code, c.account_no, l.account_no"/>
            <column name="description" value="All clients with an outstanding loan between cycleX and cycleY days"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="169"/>
            <column name="report_name" value="Loan in arrears"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="NonTriggered"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT mc.id AS id, mc.firstname AS firstName, mc.middlename AS middleName, mc.lastname AS lastName, mc.display_name AS fullName, mc.mobile_no AS mobileNo, ml.principal_amount AS loanAmount, (COALESCE(ml.principal_outstanding_derived, 0) + COALESCE(ml.interest_outstanding_derived, 0) + COALESCE(ml.fee_charges_outstanding_derived, 0) + COALESCE(ml.penalty_charges_outstanding_derived, 0)) AS loanOutstanding, ml.principal_disbursed_derived AS loanDisbursed, laa.overdue_since_date_derived AS paymentDueDate, COALESCE(laa.total_overdue_derived, 0) AS totalDue, ounder.id AS officeNumber, ml.account_no AS loanAccountId, gua.lastname AS guarantorLastName, COUNT(gua.id) AS numberOfGuarantors, g.display_name AS groupName FROM m_office mo JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(mo.hierarchy, '%') INNER JOIN m_client mc ON mc.office_id=ounder.id INNER JOIN m_loan ml ON ml.client_id = mc.id INNER JOIN r_enum_value rev ON rev.enum_id=ml.loan_status_id AND rev.enum_name = 'loan_status_id' INNER JOIN m_loan_arrears_aging laa ON laa.loan_id=ml.id LEFT JOIN m_currency cur ON cur.code = ml.currency_code LEFT JOIN m_group_client gc ON gc.client_id = mc.id LEFT JOIN m_group g ON g.id = gc.group_id LEFT JOIN m_staff lo ON lo.id = ml.loan_officer_id LEFT JOIN m_guarantor gua ON gua.loan_id = ml.id WHERE ml.loan_status_id=300 AND mo.id='${officeId}' AND (COALESCE(ml.loan_officer_id, -10) = ${loanOfficerId} OR '-1' = ${loanOfficerId}) AND (EXTRACT(DAY FROM (CURRENT_DATE - laa.overdue_since_date_derived::TIMESTAMP)) BETWEEN ${fromX} AND ${toY}) GROUP BY ml.id, mc.id, laa.loan_id, ounder.id, gua.id, g.id ORDER BY ounder.hierarchy, ml.currency_code, mc.account_no, ml.account_no"/>
            <column name="description" value="All clients with an outstanding loan in arrears between fromX and toY days"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="170"/>
            <column name="report_name" value="Loan payments due"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="NonTriggered"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT cl.id AS id, cl.firstname  AS firstName, cl.middlename  AS middleName, cl.lastname AS lastName, cl.display_name AS fullName, cl.mobile_no AS mobileNo, l.principal_amount AS loanAmount, of.id AS officeNumber, (COALESCE(l.principal_outstanding_derived, 0) + COALESCE(l.interest_outstanding_derived, 0) + COALESCE(l.fee_charges_outstanding_derived, 0) + COALESCE(l.penalty_charges_outstanding_derived, 0)) AS loanOutstanding, l.principal_disbursed_derived AS loanDisbursed, ls.duedate AS paymentDueDate, (COALESCE(SUM(ls.principal_amount), 0) - COALESCE(SUM(ls.principal_writtenoff_derived), 0) + COALESCE(SUM(ls.interest_amount), 0) - COALESCE(SUM(ls.interest_writtenoff_derived), 0) - COALESCE(SUM(ls.interest_waived_derived), 0) + COALESCE(SUM(ls.fee_charges_amount), 0) - COALESCE(SUM(ls.fee_charges_writtenoff_derived), 0) - COALESCE(SUM(ls.fee_charges_waived_derived), 0) + COALESCE(SUM(ls.penalty_charges_amount), 0) - COALESCE(SUM(ls.penalty_charges_writtenoff_derived), 0) - COALESCE(SUM(ls.penalty_charges_waived_derived), 0)) AS totalDue, laa.total_overdue_derived AS totalOverdue, l.account_no AS loanAccountId, gua.lastname AS guarantorLastName, COUNT(gua.id) AS numberOfGuarantors, gp.display_name AS groupName FROM m_office of LEFT JOIN m_client cl ON of.id = cl.office_id LEFT JOIN m_loan l ON cl.id = l.client_id LEFT JOIN m_group_client gc ON gc.client_id = cl.id LEFT JOIN m_group gp ON gp.id = l.group_id LEFT JOIN m_loan_repayment_schedule ls ON l.id = ls.loan_id LEFT JOIN m_guarantor gua ON gua.loan_id = l.id INNER JOIN m_loan_arrears_aging laa ON laa.loan_id=l.id WHERE of.id = '${officeId}' AND (COALESCE (l.loan_officer_id, -10) = ${loanOfficerId} OR '-1' = ${loanOfficerId}) AND (EXTRACT(DAY FROM (CURRENT_DATE - ls.duedate::TIMESTAMP)) BETWEEN ${fromX} AND ${toY}) AND (of.hierarchy LIKE CONCAT((SELECT ino.hierarchy FROM m_office ino WHERE ino.id = '${officeId}'), '%')) GROUP BY l.id, cl.id, of.id, ls.id, laa.loan_id, gua.id, gp.id ORDER BY of.hierarchy, l.currency_code, cl.account_no, l.account_no"/>
            <column name="description" value="All clients with an unpaid installment due on their loan between fromX and toY days"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="171"/>
            <column name="report_name" value="Dormant Prospects"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="NonTriggered"/>
            <column name="report_category" value="Client"/>
            <column name="report_sql" value="SELECT c.id AS id, CONCAT(REPEAT('..', ((LENGTH(ounder.hierarchy) - LENGTH(REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS officeName, c.firstname AS firstName, c.middlename AS middleName, c.lastname AS lastName, c.display_name AS fullName, c.mobile_no AS mobileNo, o.id AS officeNumber, DATE_PART('MONTH', AGE(CURRENT_DATE, c.activation_date)) AS dormant FROM m_office o JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(o.hierarchy, '%') JOIN m_client c ON c.office_id = ounder.id LEFT JOIN r_enum_value r ON r.enum_name = 'status_enum' AND r.enum_id = c.status_enum LEFT JOIN m_loan l ON l.client_id = c.id WHERE o.id = '${officeId}' AND c.status_enum = 300 AND (COALESCE(c.staff_id, -10) = ${loanOfficerId} OR '-1' = ${loanOfficerId}) AND l.client_id IS NULL AND (DATE_PART('MONTH', AGE(CURRENT_DATE, c.activation_date)) &gt; 3) GROUP BY c.id, ounder.id, o.id ORDER BY ounder.hierarchy, c.account_no"/>
            <column name="description" value="All individuals who have not yet received a loan but were also entered into the system more than 3 months"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="172"/>
            <column name="report_name" value="Active group leaders"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="NonTriggered"/>
            <column name="report_category" value="Client"/>
            <column name="report_sql" value="SELECT c.id AS id, c.firstname AS firstName, c.middlename AS middleName, c.lastname AS lastName, c.display_name AS fullName, c.mobile_no AS mobileNo, CONCAT(REPEAT('..', ((LENGTH(ounder.hierarchy) - LENGTH(REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS officeName, o.id AS officeNumber FROM m_office o JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(o.hierarchy, '%') JOIN m_group g ON g.office_id = ounder.id JOIN m_client c ON c.office_id = ounder.id LEFT JOIN m_group_client gc ON gc.group_id = g.id AND gc.client_id = c.id LEFT JOIN m_group_roles gr ON gr.group_id = g.id AND gr.client_id = c.id LEFT JOIN m_staff ms ON ms.id = c.staff_id LEFT JOIN r_enum_value r ON r.enum_name = 'status_enum' AND r.enum_id = c.status_enum LEFT JOIN m_code_value cv ON cv.id = gr.role_cv_id LEFT JOIN m_code code ON code.id = cv.code_id WHERE o.id = '${officeId}' AND g.status_enum = 300 AND c.status_enum = 300 AND (COALESCE(c.staff_id, -10) = ${loanOfficerId} OR '-1' = ${loanOfficerId}) AND code.code_name = 'GROUPROLE' AND cv.code_value = 'Leader' GROUP BY c.id, ounder.id, o.id ORDER BY ounder.hierarchy, c.account_no"/>
            <column name="description" value="All active group chairmen"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="173"/>
            <column name="report_name" value="Loan payments due (Overdue Loans)"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="NonTriggered"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT mc.id AS id, mc.firstname AS firstName, mc.middlename AS middleName, mc.lastname AS lastName, mc.display_name AS fullName, mc.mobile_no AS mobileNo, ml.principal_amount AS loanAmount, (COALESCE(ml.principal_outstanding_derived, 0) + COALESCE(ml.interest_outstanding_derived, 0) + COALESCE(ml.fee_charges_outstanding_derived, 0) + COALESCE(ml.penalty_charges_outstanding_derived, 0)) AS loanOutstanding, ml.principal_disbursed_derived AS loanDisbursed, laa.overdue_since_date_derived AS paymentDueDate, (COALESCE(SUM(ls.principal_amount), 0) - COALESCE(SUM(ls.principal_writtenoff_derived), 0) + COALESCE(SUM(ls.interest_amount), 0) - COALESCE(SUM(ls.interest_writtenoff_derived), 0) - COALESCE(SUM(ls.interest_waived_derived), 0) + COALESCE(SUM(ls.fee_charges_amount), 0) - COALESCE(SUM(ls.fee_charges_writtenoff_derived), 0) - COALESCE(SUM(ls.fee_charges_waived_derived), 0) + COALESCE(SUM(ls.penalty_charges_amount), 0) - COALESCE(SUM(ls.penalty_charges_writtenoff_derived), 0) - COALESCE(SUM(ls.penalty_charges_waived_derived), 0)) AS totalDue, laa.total_overdue_derived AS totalOverdue, ounder.id AS officeNumber, ml.account_no AS loanAccountId, gua.lastname AS guarantorLastName, COUNT(gua.id) AS numberOfGuarantors, g.display_name AS groupName FROM m_office mo JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(mo.hierarchy, '%') INNER JOIN m_client mc ON mc.office_id = ounder.id INNER JOIN m_loan ml ON ml.client_id = mc.id INNER JOIN r_enum_value rev ON rev.enum_id = ml.loan_status_id AND rev.enum_name = 'loan_status_id' INNER JOIN m_loan_arrears_aging laa ON laa.loan_id = ml.id LEFT JOIN m_loan_repayment_schedule ls ON ls.loan_id = ml.id LEFT JOIN m_currency cur ON cur.code = ml.currency_code LEFT JOIN m_group_client gc ON gc.client_id = mc.id LEFT JOIN m_group g ON g.id = gc.group_id LEFT JOIN m_staff lo ON lo.id = ml.loan_officer_id LEFT JOIN m_guarantor gua ON gua.loan_id = ml.id WHERE ml.loan_status_id = 300 AND mo.id = '${officeId}' AND (COALESCE(ml.loan_officer_id, -10) = ${loanOfficerId} OR '-1' = ${loanOfficerId}) AND (EXTRACT(DAY FROM (CURRENT_DATE - ls.duedate::TIMESTAMP)) BETWEEN ${fromX} AND ${toY}) AND (EXTRACT(DAY FROM (CURRENT_DATE - laa.overdue_since_date_derived::TIMESTAMP)) BETWEEN ${overdueX} AND ${overdueY}) GROUP BY ml.id, mc.id, laa.loan_id, ounder.id, gua.id, g.id ORDER BY ounder.hierarchy, ml.currency_code, mc.account_no, ml.account_no"/>
            <column name="description" value="Loan Payments Due between fromX to toY days for clients in arrears between overdueX and overdueY days"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="174"/>
            <column name="report_name" value="Loan payments received (Active Loans)"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="NonTriggered"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT mc.id AS id, mc.firstname AS firstName, mc.middlename AS middleName, mc.lastname AS lastName, mc.display_name AS fullName, mc.mobile_no AS mobileNo, ml.principal_amount AS loanAmount, (COALESCE(ml.principal_outstanding_derived, 0) + COALESCE(ml.interest_outstanding_derived, 0) + COALESCE(ml.fee_charges_outstanding_derived, 0) + COALESCE(ml.penalty_charges_outstanding_derived, 0)) AS loanOutstanding, ounder.id AS officeNumber, ml.account_no AS loanAccountNumber, SUM(lt.amount) AS repaymentAmount FROM m_office mo JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(mo.hierarchy, '%') INNER JOIN m_client mc ON mc.office_id = ounder.id INNER JOIN m_loan ml ON ml.client_id = mc.id INNER JOIN r_enum_value rev ON rev.enum_id = ml.loan_status_id AND rev.enum_name = 'loan_status_id' INNER JOIN m_loan_transaction lt ON lt.loan_id = ml.id INNER JOIN m_appuser au ON au.id = lt.appuser_id LEFT JOIN m_loan_arrears_aging laa ON laa.loan_id = ml.id LEFT JOIN m_payment_detail mpd ON mpd.id = lt.payment_detail_id LEFT JOIN m_currency cur ON cur.code = ml.currency_code LEFT JOIN m_group_client gc ON gc.client_id = mc.id LEFT JOIN m_group g ON g.id = gc.group_id LEFT JOIN m_staff lo ON lo.id = ml.loan_officer_id LEFT JOIN m_guarantor gua ON gua.loan_id = ml.id WHERE ml.loan_status_id = 300 AND mo.id = '${officeId}' AND (COALESCE(ml.loan_officer_id, -10) = ${loanOfficerId} OR '-1' = ${loanOfficerId}) AND (EXTRACT(DAY FROM (CURRENT_DATE - lt.transaction_date::TIMESTAMP)) BETWEEN 9${fromX} AND ${toY}) AND lt.is_reversed = false AND lt.transaction_type_enum = 2 AND laa.loan_id IS NULL GROUP BY ml.id, mc.id, ounder.id ORDER BY ounder.hierarchy, ml.currency_code, mc.account_no, ml.account_no"/>
            <column name="description" value="Payments received in the last fromX to toY days for any loan with the status Active (on-time)"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="175"/>
            <column name="report_name" value="Loan payments received (Overdue Loans)"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="NonTriggered"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT ml.id AS loanId, mc.id AS id, mc.firstname AS firstName, mc.middlename AS middleName, mc.lastname AS lastName, mc.display_name AS fullName, mc.mobile_no AS mobileNo, ml.principal_amount AS loanAmount, (COALESCE(ml.principal_outstanding_derived, 0) + COALESCE(ml.interest_outstanding_derived, 0) + COALESCE(ml.fee_charges_outstanding_derived, 0) + COALESCE(ml.penalty_charges_outstanding_derived, 0)) AS loanOutstanding, ounder.id AS officeNumber, ml.account_no AS loanAccountNumber, SUM(lt.amount) AS repaymentAmount FROM m_office mo JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(mo.hierarchy, '%') INNER JOIN m_client mc ON mc.office_id = ounder.id INNER JOIN m_loan ml ON ml.client_id = mc.id INNER JOIN r_enum_value rev ON rev.enum_id = ml.loan_status_id AND rev.enum_name = 'loan_status_id' INNER JOIN m_loan_arrears_aging laa ON laa.loan_id = ml.id INNER JOIN m_loan_transaction lt ON lt.loan_id = ml.id INNER JOIN m_appuser au ON au.id = lt.appuser_id LEFT JOIN m_payment_detail mpd ON mpd.id = lt.payment_detail_id LEFT JOIN m_currency cur ON cur.code = ml.currency_code LEFT JOIN m_group_client gc ON gc.client_id = mc.id LEFT JOIN m_group g ON g.id = gc.group_id LEFT JOIN m_staff lo ON lo.id = ml.loan_officer_id LEFT JOIN m_guarantor gua ON gua.loan_id = ml.id WHERE ml.loan_status_id = 300 AND mo.id = '${officeId}' AND (COALESCE(ml.loan_officer_id, -10) = ${loanOfficerId} OR '-1' = ${loanOfficerId}) AND (EXTRACT(DAY FROM(CURRENT_DATE - lt.transaction_date::TIMESTAMP)) BETWEEN ${fromX} AND ${toY}) AND (EXTRACT(DAY FROM(CURRENT_DATE - laa.overdue_since_date_derived::TIMESTAMP)) BETWEEN ${overdueX} AND ${overdueY}) AND lt.is_reversed = false AND lt.transaction_type_enum = 2 GROUP BY ml.id, mc.id, ounder.id ORDER BY ounder.hierarchy, ml.currency_code, mc.account_no, ml.account_no"/>
            <column name="description" value="Payments received in the last fromX to toY days for any loan with the status Overdue (arrears) between overdueX and overdueY days"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="176"/>
            <column name="report_name" value="Happy Birthday"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="NonTriggered"/>
            <column name="report_category" value="Client"/>
            <column name="report_sql" value="SELECT c.id AS id, c.firstname AS firstName, c.middlename AS middleName,  c.lastname AS lastName, c.display_name AS fullName,  c.mobile_no AS mobileNo, CONCAT(REPEAT('..', ((LENGTH(ounder.hierarchy) - LENGTH( REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS officeName,   o.id AS officeNumber, c.date_of_birth AS dateOfBirth,  CASE WHEN c.date_of_birth IS NULL THEN 0 ELSE CEIL(EXTRACT(DAY FROM (CURRENT_DATE - c.date_of_birth))/365) END AS age  FROM m_office o  JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(o.hierarchy, '%')  JOIN m_client c ON c.office_id = ounder.id  LEFT JOIN r_enum_value r ON r.enum_name = 'status_enum' AND r.enum_id = c.status_enum  LEFT JOIN m_staff ms ON ms.id = c.staff_id  WHERE o.id = '${officeId}' AND c.status_enum = 300 AND (COALESCE(c.staff_id, -10) = -1 OR '-1' = -1) AND c.date_of_birth IS NOT NULL AND (DATE_TRUNC('day', c.date_of_birth)=DATE_TRUNC('day', NOW())) AND (DATE_TRUNC('month', c.date_of_birth)=DATE_TRUNC('month', NOW()))  ORDER BY ounder.hierarchy, c.account_no"/>
            <column name="description" value="This sends a message to all clients with the status Active on their Birthday"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="177"/>
            <column name="report_name" value="Loan fully repaid"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="NonTriggered"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT c.id AS id, c.firstname AS firstName, c.middlename AS middleName, c.lastname AS lastName, c.display_name AS fullName, c.mobile_no AS mobileNo, l.principal_amount AS loanAmount, (COALESCE(l.principal_outstanding_derived, 0) + COALESCE(l.interest_outstanding_derived, 0) + COALESCE(l.fee_charges_outstanding_derived, 0) + COALESCE(l.penalty_charges_outstanding_derived, 0)) AS loanOutstanding, l.principal_disbursed_derived AS loanDisbursed, o.id AS officeNumber, l.account_no AS loanAccountId, gua.lastname AS guarantorLastName, COUNT(gua.id) AS numberOfGuarantors, ls.duedate AS dueDate, laa.total_overdue_derived AS totalDue, gp.display_name AS groupName, l.total_repayment_derived AS &quot;totalFullyPaid&quot; FROM m_office o JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(o.hierarchy, '%') JOIN m_client c ON c.office_id = ounder.id JOIN m_loan l ON l.client_id = c.id LEFT JOIN m_staff lo ON lo.id = l.loan_officer_id LEFT JOIN m_currency cur ON cur.code = l.currency_code LEFT JOIN m_group_client gc ON gc.client_id = c.id LEFT JOIN m_group gp ON gp.id = l.group_id LEFT JOIN m_loan_repayment_schedule ls ON l.id = ls.loan_id LEFT JOIN m_guarantor gua ON gua.loan_id = l.id LEFT JOIN m_loan_arrears_aging laa ON laa.loan_id = l.id WHERE o.id = '${officeId}' AND (COALESCE(l.loan_officer_id, -10) = ${loanOfficerId} OR '-1' = ${loanOfficerId}) AND (EXTRACT(DAY FROM (CURRENT_DATE - l.closedon_date::TIMESTAMP)) BETWEEN ${fromX} AND ${toY}) AND (l.loan_status_id IN (600, 700)) GROUP BY l.id, c.id, o.id, gua.id, ls.id,laa.loan_id, gp.id, ounder.id ORDER BY ounder.hierarchy, l.currency_code, c.account_no, l.account_no"/>
            <column name="description" value="All loans that have been fully repaid (Closed or Overpaid) in the last fromX to toY days"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="178"/>
            <column name="report_name" value="Loan outstanding after final instalment date"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="NonTriggered"/>
            <column name="report_category" value="Loan"/>
            <column name="report_sql" value="SELECT c.id AS id, c.firstname AS firstName, c.middlename AS middleName, c.lastname AS lastName, c.display_name AS fullName, c.mobile_no AS mobileNo, l.principal_amount AS loanAmount, o.id AS officeNumber, (COALESCE(l.principal_outstanding_derived, 0) + COALESCE(l.interest_outstanding_derived, 0) + COALESCE(l.fee_charges_outstanding_derived, 0) + COALESCE(l.penalty_charges_outstanding_derived, 0)) AS loanOutstanding, l.principal_disbursed_derived AS loanDisbursed, ls.duedate AS paymentDueDate, (COALESCE(SUM(ls.principal_amount), 0) - COALESCE(SUM(ls.principal_writtenoff_derived), 0) + COALESCE(SUM(ls.interest_amount), 0) - COALESCE(SUM(ls.interest_writtenoff_derived), 0) - COALESCE(SUM(ls.interest_waived_derived), 0) + COALESCE(SUM(ls.fee_charges_amount), 0) - COALESCE(SUM(ls.fee_charges_writtenoff_derived), 0) - COALESCE(SUM(ls.fee_charges_waived_derived), 0) + COALESCE(SUM(ls.penalty_charges_amount), 0) - COALESCE(SUM(ls.penalty_charges_writtenoff_derived), 0) - COALESCE(SUM(ls.penalty_charges_waived_derived), 0)) AS totalDue, laa.total_overdue_derived AS totalOverdue, l.account_no AS loanAccountId, gua.lastname AS guarantorLastName, COUNT(gua.id) AS numberOfGuarantors, gp.display_name AS groupName FROM m_office o JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(o.hierarchy, '%') JOIN m_client c ON c.office_id = ounder.id JOIN m_loan l ON l.client_id = c.id LEFT JOIN m_staff lo ON lo.id = l.loan_officer_id LEFT JOIN m_currency cur ON cur.code = l.currency_code LEFT JOIN m_loan_arrears_aging laa ON laa.loan_id = l.id LEFT JOIN m_group_client gc ON gc.client_id = c.id LEFT JOIN m_group gp ON gp.id = l.group_id LEFT JOIN m_loan_repayment_schedule ls ON l.id = ls.loan_id LEFT JOIN m_guarantor gua ON gua.loan_id = l.id WHERE o.id = '${officeId}' AND (COALESCE(l.loan_officer_id, -10) = ${loanOfficerId} OR '-1' = ${loanOfficerId}) AND l.loan_status_id = 300 AND l.expected_maturedon_date &lt; CURRENT_DATE AND (EXTRACT(DAY FROM (CURRENT_DATE - l.expected_maturedon_date::TIMESTAMP)) BETWEEN ${fromX} AND ${toY}) GROUP BY l.id, c.id, o.id, ls.id, laa.loan_id, gua.id, gp.id, ounder.id ORDER BY ounder.hierarchy, l.currency_code, c.account_no, l.account_no"/>
            <column name="description" value="All active loans (with an outstanding balance) between fromX to toY days after the final instalment date on their loan schedule"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="179"/>
            <column name="report_name" value="Loan Repayment"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="Triggered"/>
            <column name="report_category"/>
            <column name="report_sql" value="select ml.id as loanId, mc.id, mc.firstname, COALESCE(mc.middlename, '') as middlename, mc.lastname, mc.display_name as FullName, mobile_no as mobileNo, mc.group_name as GroupName, round(ml.principal_amount, ml.currency_digits) as LoanAmount, round(ml.&quot;total_outstanding_derived&quot;, ml.currency_digits) as LoanOutstanding, ml.&quot;account_no&quot; as LoanAccountId, round(mlt.amountPaid, ml.currency_digits) as repaymentAmount FROM m_office mo JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(mo.hierarchy, '%') AND ounder.hierarchy like CONCAT('.', '%') LEFT JOIN (select ml.id as loanId, COALESCE(mc.id, mc2.id) as id, COALESCE(mc.firstname, mc2.firstname) as firstname, COALESCE(mc.middlename, COALESCE(mc2.middlename, (''))) as middlename, COALESCE(mc.lastname, mc2.lastname) as lastname, COALESCE(mc.display_name, mc2.display_name) as display_name, COALESCE(mc.status_enum, mc2.status_enum) as status_enum, COALESCE(mc.mobile_no, mc2.mobile_no) as mobile_no, COALESCE(mg.office_id, mc2.office_id) as office_id, COALESCE(mg.staff_id, mc2.staff_id) as staff_id, mg.id as group_id, mg.display_name as group_name from m_loan ml left join m_group mg on mg.id = ml.group_id left join m_group_client mgc on mgc.group_id = mg.id left join m_client mc on mc.id = mgc.client_id left join m_client mc2 on mc2.id = ml.client_id order by loanId) mc on mc.office_id = ounder.id right join m_loan as ml on mc.loanId = ml.id right join(select mlt.amount as amountPaid, mlt.id, mlt.loan_id from m_loan_transaction mlt where mlt.is_reversed = false group by mlt.loan_id, mlt.id) as mlt on mlt.loan_id = ml.id right join m_loan_repayment_schedule as mls1 on ml.id = mls1.loan_id and mls1.&quot;completed_derived&quot; = false and mls1.installment = (SELECT MIN(installment) from m_loan_repayment_schedule where loan_id = ml.id and duedate &lt;= CURRENT_DATE and completed_derived = false) where mc.status_enum = 300 and mobile_no is not null and ml.&quot;loan_status_id&quot; = 300 and (mo.id = '${officeId}' or '${officeId}' = -1) and (mc.staff_id = ${loanOfficerId} or ${loanOfficerId} = -1) and (ml.loan_type_enum = ${loanType} or ${loanType} = -1) and ml.id in (select mla.loan_id from m_loan_arrears_aging mla) group by ml.id, mc.id, mc.firstname, mc.middlename, mc.lastname, mc.display_name, mc.mobile_no, mc.group_name, mlt.amountPaid"/>
            <column name="description" value="Loan Repayment"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="180"/>
            <column name="report_name" value="Loan Approved"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="Triggered"/>
            <column name="report_category"/>
            <column name="report_sql" value="SELECT mc.id, mc.firstname, mc.middlename as middlename, mc.lastname, mc.display_name as FullName, mc.mobile_no as mobileNo, mc.group_name as GroupName, mo.name as officename, ml.id as loanId, ml.account_no as accountnumber, ml.principal_amount_proposed as loanamount, ml.annual_nominal_interest_rate as annualinterestrate FROM m_office mo JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(mo.hierarchy, '%') AND ounder.hierarchy like CONCAT('.', '%') LEFT JOIN ( select  ml.id as loanId, COALESCE(mc.id,mc2.id) as id, COALESCE(mc.firstname,mc2.firstname) as firstname, COALESCE(mc.middlename,COALESCE(mc2.middlename,(''))) as middlename,  COALESCE(mc.lastname,mc2.lastname) as lastname,  COALESCE(mc.display_name,mc2.display_name) as display_name,  COALESCE(mc.status_enum,mc2.status_enum) as status_enum, COALESCE(mc.mobile_no,mc2.mobile_no) as mobile_no, COALESCE(mg.office_id,mc2.office_id) as office_id, COALESCE(mg.staff_id,mc2.staff_id) as staff_id, mg.id as group_id, mg.display_name as group_name from m_loan ml left join m_group mg on mg.id = ml.group_id left join m_group_client mgc on mgc.group_id = mg.id left join m_client mc on mc.id = mgc.client_id left join m_client mc2 on mc2.id = ml.client_id order by loanId ) mc on mc.office_id = ounder.id  left join m_loan ml on ml.id = mc.loanId WHERE mc.status_enum = 300 and mc.mobile_no is not null and (mo.id = '${officeId}' or '${officeId}' = -1) and (mc.staff_id = ${loanOfficerId} or ${loanOfficerId} = -1)and (ml.id = ${loanId} or ${loanId} = -1)and (mc.id = ${clientId} or ${clientId} = -1)and (mc.group_id = ${groupId} or ${groupId} = -1)and (ml.loan_type_enum = ${loanType} or ${loanType} = -1)"/>
            <column name="description" value="Loan and client data of approved loan"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="181"/>
            <column name="report_name" value="Loan Rejected"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="Triggered"/>
            <column name="report_category"/>
            <column name="report_sql" value="SELECT mc.id, mc.firstname, mc.middlename as middlename, mc.lastname, mc.display_name as FullName, mc.mobile_no as mobileNo, mc.group_name as GroupName,  mo.name as officename, ml.id as loanId, ml.account_no as accountnumber, ml.principal_amount_proposed as loanamount, ml.annual_nominal_interest_rate as annualinterestrate  FROM m_office mo  JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(mo.hierarchy, '%')  AND ounder.hierarchy like CONCAT('.', '%')  LEFT JOIN (  select   ml.id as loanId, COALESCE(mc.id,mc2.id) as id, COALESCE(mc.firstname,mc2.firstname) as firstname, COALESCE(mc.middlename,COALESCE(mc2.middlename,(''))) as middlename,   COALESCE(mc.lastname,mc2.lastname) as lastname,   COALESCE(mc.display_name,mc2.display_name) as display_name,   COALESCE(mc.status_enum,mc2.status_enum) as status_enum,  COALESCE(mc.mobile_no,mc2.mobile_no) as mobile_no,  COALESCE(mg.office_id,mc2.office_id) as office_id,  COALESCE(mg.staff_id,mc2.staff_id) as staff_id, mg.id as group_id,  mg.display_name as group_name  from m_loan ml  left join m_group mg on mg.id = ml.group_id  left join m_group_client mgc on mgc.group_id = mg.id  left join m_client mc on mc.id = mgc.client_id  left join m_client mc2 on mc2.id = ml.client_id  order by loanId  ) mc on mc.office_id = ounder.id  left join m_loan ml on ml.id = mc.loanId  WHERE mc.status_enum = 300 and mc.mobile_no is not null  and (mo.id = '${officeId}' or '${officeId}' = -1)  and (mc.staff_id = ${loanOfficerId} or ${loanOfficerId} = -1) and (ml.id = ${loanId} or ${loanId} = -1) and (mc.id = ${clientId} or ${clientId} = -1) and (mc.group_id = ${groupId} or ${groupId} = -1)  and (ml.loan_type_enum = ${loanType} or ${loanType} = -1)"/>
            <column name="description" value="Loan and client data of rejected loan"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="falsee"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="182"/>
            <column name="report_name" value="Client Rejected"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="Triggered"/>
            <column name="report_category" value="Client"/>
            <column name="report_sql" value="SELECT c.id AS id,   c.firstname AS firstName,  c.middlename AS middleName,  c.lastname AS lastName,  c.display_name AS fullName,  c.mobile_no AS mobileNo, CONCAT(REPEAT('..', ((LENGTH(ounder.hierarchy) - LENGTH( REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS officeName,   o.id AS officeNumber  FROM m_office o  JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(o.hierarchy, '%')  JOIN m_client c ON c.office_id = ounder.id  LEFT JOIN r_enum_value r ON r.enum_name = 'status_enum' AND r.enum_id = c.status_enum  WHERE o.id = '${officeId}' AND c.id = ${clientId} AND (COALESCE(c.staff_id, -10) = ${loanOfficerId} OR '-1' = ${loanOfficerId})"/>
            <column name="description" value="Client Rejection"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="183"/>
            <column name="report_name" value="Client Activated"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="Triggered"/>
            <column name="report_category" value="Client"/>
            <column name="report_sql" value="SELECT c.id AS id,   c.firstname AS firstName,  c.middlename AS middleName,  c.lastname AS lastName,  c.display_name AS fullName,  c.mobile_no AS mobileNo, CONCAT(REPEAT('..', ((LENGTH(ounder.hierarchy) - LENGTH( REPLACE(ounder.hierarchy, '.', '')) - 1))), ounder.name) AS officeName,   o.id AS officeNumber  FROM m_office o  JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(o.hierarchy, '%')  JOIN m_client c ON c.office_id = ounder.id  LEFT JOIN r_enum_value r ON r.enum_name = 'status_enum' AND r.enum_id = c.status_enum  WHERE o.id = '${officeId}' AND c.id = ${clientId} AND (COALESCE(c.staff_id, -10) = ${loanOfficerId} OR '-1' = ${loanOfficerId})"/>
            <column name="description" value="Client Activation"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="184"/>
            <column name="report_name" value="Savings Rejected"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="Triggered"/>
            <column name="report_category" value="Savings"/>
            <column name="report_sql" value="SELECT   c.id AS id,  c.firstname AS firstName, c.middlename AS middleName,  c.lastname AS lastName, c.display_name AS fullName,  c.mobile_no AS mobileNo, s.account_no AS savingsAccountNo,  ounder.id AS officeNumber,  ounder.name AS officeName    FROM m_office o JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(o.hierarchy, '%')  JOIN m_client c ON c.office_id = ounder.id  JOIN m_savings_account s ON s.client_id = c.id JOIN m_savings_product sp ON sp.id = s.product_id  LEFT JOIN m_staff st ON st.id = s.field_officer_id  LEFT JOIN m_currency cur ON cur.code = s.currency_code  WHERE o.id = '${officeId}' AND (COALESCE(s.field_officer_id, -10) = ${loanOfficerId} OR '-1' = ${loanOfficerId}) AND s.id = ${savingsId}"/>
            <column name="description" value="Savings Rejected"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="185"/>
            <column name="report_name" value="Savings Activated"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="Triggered"/>
            <column name="report_category" value="Savings"/>
            <column name="report_sql" value="SELECT   c.id AS id,  c.firstname AS firstName, c.middlename AS middleName,  c.lastname AS lastName, c.display_name AS fullName,  c.mobile_no AS mobileNo, s.account_no AS savingsAccountNo,  ounder.id AS officeNumber,  ounder.name AS officeName    FROM m_office o JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(o.hierarchy, '%')  JOIN m_client c ON c.office_id = ounder.id  JOIN m_savings_account s ON s.client_id = c.id JOIN m_savings_product sp ON sp.id = s.product_id  LEFT JOIN m_staff st ON st.id = s.field_officer_id  LEFT JOIN m_currency cur ON cur.code = s.currency_code  WHERE o.id = '${officeId}' AND (COALESCE(s.field_officer_id, -10) = ${loanOfficerId} OR '-1' = ${loanOfficerId}) AND s.id = ${savingsId}"/>
            <column name="description" value="Savings Activation"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="186"/>
            <column name="report_name" value="Savings Deposit"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="Triggered"/>
            <column name="report_category"/>
            <column name="report_sql" value="SELECT sc.savingsId AS savingsId, sc.id AS clientId, sc.firstname, COALESCE(sc.middlename,'') AS middlename, sc.lastname, sc.display_name AS FullName, sc.mobile_no AS mobileNo, ms.&quot;account_no&quot; AS savingsAccountNo, ROUND(mst.amountPaid, ms.currency_digits) AS depositAmount, ms.account_balance_derived AS balance, mst.transactionDate AS transactionDate FROM m_office mo JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(mo.hierarchy, '%') AND ounder.hierarchy LIKE CONCAT('.', '%') LEFT JOIN (SELECT sa.id AS savingsId, mc.id AS id, mc.firstname AS firstname, mc.middlename AS middlename, mc.lastname AS lastname, mc.display_name AS display_name, mc.status_enum AS status_enum, mc.mobile_no AS mobile_no, mc.office_id AS office_id, mc.staff_id AS staff_id FROM m_savings_account sa LEFT JOIN m_client mc ON mc.id = sa.client_id ORDER BY savingsId) sc ON sc.office_id = ounder.id RIGHT JOIN m_savings_account AS ms ON sc.savingsId = ms.id RIGHT JOIN(SELECT st.amount AS amountPaid, st.id, st.savings_account_id, st.id AS savingsTransactionId, st.transaction_date AS transactionDate FROM m_savings_account_transaction st WHERE st.is_reversed = false GROUP BY st.savings_account_id, st.amount, st.id) AS mst ON mst.savings_account_id = ms.id WHERE sc.mobile_no IS NOT NULL AND (mo.id = '${officeId}' OR '${officeId}' = -1) AND (sc.staff_id = ${loanOfficerId} OR ${loanOfficerId} = -1) AND mst.savingsTransactionId = ${savingsTransactionId}"/>
            <column name="description" value="Savings Deposit"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="187"/>
            <column name="report_name" value="Savings Withdrawal"/>
            <column name="report_type" value="SMS"/>
            <column name="report_subtype" value="Triggered"/>
            <column name="report_category"/>
            <column name="report_sql" value="SELECT sc.savingsId AS savingsId, sc.id AS clientId, sc.firstname, COALESCE(sc.middlename,'') AS middlename, sc.lastname, sc.display_name AS FullName, sc.mobile_no AS mobileNo,  ms.&quot;account_no&quot; AS savingsAccountNo, ROUND(mst.amountPaid, ms.currency_digits) AS withdrawAmount, ms.account_balance_derived AS balance, mst.transactionDate AS transactionDate FROM m_office mo JOIN m_office ounder ON ounder.hierarchy LIKE CONCAT(mo.hierarchy, '%') AND ounder.hierarchy LIKE CONCAT('.', '%') LEFT JOIN (SELECT sa.id AS savingsId, mc.id AS id, mc.firstname AS firstname, mc.middlename AS middlename, mc.lastname AS lastname, mc.display_name AS display_name, mc.status_enum AS status_enum, mc.mobile_no AS mobile_no, mc.office_id AS office_id, mc.staff_id AS staff_id FROM m_savings_account sa LEFT JOIN m_client mc ON mc.id = sa.client_id ORDER BY savingsId) sc ON sc.office_id = ounder.id RIGHT JOIN m_savings_account AS ms ON sc.savingsId = ms.id RIGHT JOIN(SELECT st.amount AS amountPaid, st.id, st.savings_account_id, st.id AS savingsTransactionId, st.transaction_date AS transactionDate FROM m_savings_account_transaction st WHERE st.is_reversed = false GROUP BY st.savings_account_id, st.amount, st.id) AS mst ON mst.savings_account_id = ms.id WHERE sc.mobile_no IS NOT NULL AND (mo.id = '${officeId}' OR '${officeId}' = -1) AND (sc.staff_id = ${loanOfficerId} OR ${loanOfficerId} = -1) AND mst.savingsTransactionId = ${savingsTransactionId}"/>
            <column name="description" value="Savings Withdrawal"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="188"/>
            <column name="report_name" value="ReportCategoryList"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="(NULL)"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="189"/>
            <column name="report_name" value="FullReportList"/>
            <column name="report_type" value="Table"/>
            <column name="report_subtype"/>
            <column name="report_category" value="(NULL)"/>
            <column name="report_sql" value="(NULL)"/>
            <column name="description" value="(NULL)"/>
            <column name="core_report" valueBoolean="true"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="190"/>
            <column name="report_name" value="Loan Approved - Email"/>
            <column name="report_type" value="Email"/>
            <column name="report_subtype" value="Triggered"/>
            <column name="report_category"/>
            <column name="report_sql" value="select  ml.id as loanId,  COALESCE(mc.id,mc2.id) as id,  COALESCE(mc.firstname,mc2.firstname) as firstname,  \nCOALESCE(mc.middlename,mc2.middlename,(\'\')) as middlename, COALESCE(mc.lastname,mc2.lastname) as lastname,  \nCOALESCE(mc.display_name,mc2.display_name) as display_name, COALESCE(mc.status_enum,mc2.status_enum) as status_enum, \nCOALESCE(mc.mobile_no,mc2.mobile_no) as mobile_no, COALESCE(mg.office_id,mc2.office_id) as office_id, COALESCE(mg.staff_id,mc2.staff_id) as staff_id, \n mg.id as group_id, mg.display_name as group_name, COALESCE(mc.email_address,mc2.email_address) as emailAddress\nfrom m_loan ml left join m_group mg on mg.id = ml.group_id \n left join m_group_client mgc on mgc.group_id = mg.id \nleft join m_client mc on mc.id = mgc.client_id \n left join m_client mc2 on mc2.id = ml.client_id\nWHERE (mc.status_enum = 300 or mc2.status_enum = 300) and (mc.email_address is not null or mc2.email_address is not null) and ml.id = ${loanId}\n"/>
            <column name="description" value="Loan and client data of approved loan"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="191"/>
            <column name="report_name" value="Loan Rejected - Email"/>
            <column name="report_type" value="Email"/>
            <column name="report_subtype" value="Triggered"/>
            <column name="report_category"/>
            <column name="report_sql" value="select  ml.id as loanId,  COALESCE(mc.id,mc2.id) as id,  COALESCE(mc.firstname,mc2.firstname) as firstname,  \nCOALESCE(mc.middlename,mc2.middlename,(\'\')) as middlename, COALESCE(mc.lastname,mc2.lastname) as lastname,  \nCOALESCE(mc.display_name,mc2.display_name) as display_name, COALESCE(mc.status_enum,mc2.status_enum) as status_enum, \nCOALESCE(mc.mobile_no,mc2.mobile_no) as mobile_no, COALESCE(mg.office_id,mc2.office_id) as office_id, COALESCE(mg.staff_id,mc2.staff_id) as staff_id, \n mg.id as group_id, mg.display_name as group_name, COALESCE(mc.email_address,mc2.email_address) as emailAddress\nfrom m_loan ml left join m_group mg on mg.id = ml.group_id \n left join m_group_client mgc on mgc.group_id = mg.id \nleft join m_client mc on mc.id = mgc.client_id \n left join m_client mc2 on mc2.id = ml.client_id\nWHERE (mc.status_enum = 300 or mc2.status_enum = 300) and (mc.email_address is not null or mc2.email_address is not null) and ml.id = ${loanId}\n'"/>
            <column name="description" value="Loan and client data of rejected loan"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
        <insert tableName="stretchy_report">
            <column name="id" valueNumeric="192"/>
            <column name="report_name" value="Loan Repayment - Email"/>
            <column name="report_type" value="Email"/>
            <column name="report_subtype" value="Triggered"/>
            <column name="report_category"/>
            <column name="report_sql" value="select  ml.id as loanId,  COALESCE(mc.id,mc2.id) as id,  COALESCE(mc.firstname,mc2.firstname) as firstname,  \nCOALESCE(mc.middlename,mc2.middlename,(\'\')) as middlename, COALESCE(mc.lastname,mc2.lastname) as lastname,  \nCOALESCE(mc.display_name,mc2.display_name) as display_name,  COALESCE(mc.status_enum,mc2.status_enum) as status_enum, \n COALESCE(mc.mobile_no,mc2.mobile_no) as mobile_no, COALESCE(mg.office_id,mc2.office_id) as office_id, COALESCE(mg.staff_id,mc2.staff_id) as staff_id, \nmg.id as group_id, mg.display_name as group_name, COALESCE(mc.email_address,mc2.email_address) as emailAddress, lt.amount as repaymentAmount \n from m_loan_transaction lt join m_loan ml on ml.id=lt.loan_id left join m_group mg on mg.id = ml.group_id \nleft join m_group_client mgc on mgc.group_id = mg.id \n left join m_client mc on mc.id = mgc.client_id \nleft join m_client mc2 on mc2.id = ml.client_id\n WHERE (mc.status_enum = 300 or mc2.status_enum = 300) and (mc.email_address is not null or mc2.email_address is not null) and ml.id = ${loanId} and lt.id = ${loanTransactionId}\n"/>
            <column name="description" value="Loan and client data of loan repayment"/>
            <column name="core_report" valueBoolean="false"/>
            <column name="use_report" valueBoolean="true"/>
            <column name="self_service_user_report" valueBoolean="false"/>
        </insert>
    </changeSet>
    <changeSet author="fineract" id="3-postgresql" context="postgresql">
        <sql>
            SELECT SETVAL('c_cache_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM c_cache;
            SELECT SETVAL('c_configuration_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM c_configuration;
            SELECT SETVAL('c_external_service_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM c_external_service;
            SELECT SETVAL('job_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM job;
            SELECT SETVAL('job_parameters_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM job_parameters;
            SELECT SETVAL('m_appuser_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_appuser;
            SELECT SETVAL('m_code_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_code;
            SELECT SETVAL('m_code_value_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_code_value;
            SELECT SETVAL('m_creditbureau_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_creditbureau;
            SELECT SETVAL('m_creditbureau_configuration_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_creditbureau_configuration;
            SELECT SETVAL('m_currency_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_currency;
            SELECT SETVAL('m_entity_relation_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_entity_relation;
            SELECT SETVAL('m_field_configuration_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_field_configuration;
            SELECT SETVAL('m_group_level_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_group_level;
            SELECT SETVAL('m_hook_schema_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_hook_schema;
            SELECT SETVAL('m_hook_templates_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_hook_templates;
            SELECT SETVAL('m_office_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_office;
            SELECT SETVAL('m_organisation_currency_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_organisation_currency;
            SELECT SETVAL('m_password_validation_policy_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_password_validation_policy;
            SELECT SETVAL('m_payment_type_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_payment_type;
            SELECT SETVAL('m_permission_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_permission;
            SELECT SETVAL('m_provision_category_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_provision_category;
            SELECT SETVAL('m_report_mailing_job_configuration_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_report_mailing_job_configuration;
            SELECT SETVAL('m_role_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_role;
            SELECT SETVAL('m_working_days_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM m_working_days;
            SELECT SETVAL('mix_taxonomy_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM mix_taxonomy;
            SELECT SETVAL('mix_taxonomy_mapping_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM mix_taxonomy_mapping;
            SELECT SETVAL('mix_xbrl_namespace_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM mix_xbrl_namespace;
            SELECT SETVAL('ppi_scores_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM ppi_scores;
            SELECT SETVAL('ref_loan_transaction_processing_strategy_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM ref_loan_transaction_processing_strategy;
            SELECT SETVAL('scheduled_email_configuration_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM scheduled_email_configuration;
            SELECT SETVAL('scheduler_detail_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM scheduler_detail;
            SELECT SETVAL('topic_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM topic;
            SELECT SETVAL('topic_subscriber_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM topic_subscriber;
            SELECT SETVAL('stretchy_parameter_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM stretchy_parameter;
            SELECT SETVAL('stretchy_report_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM stretchy_report;
            SELECT SETVAL('stretchy_report_parameter_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM stretchy_report_parameter;
            SELECT SETVAL('twofactor_configuration_id_seq', COALESCE(MAX(id), 0)+1, false ) FROM twofactor_configuration;
        </sql>
    </changeSet>
    <changeSet author="fineract" id="883-postgresql" context="postgresql">
        <addForeignKeyConstraint baseColumnNames="report_id" baseTableName="stretchy_report_parameter"
                                 constraintName="fk_report_parameter_001" deferrable="false" initiallyDeferred="false"
                                 onDelete="CASCADE" onUpdate="NO ACTION" referencedColumnNames="id"
                                 referencedTableName="stretchy_report" validate="true"/>
    </changeSet>
    <changeSet author="fineract" id="884-postgresql" context="postgresql">
        <addForeignKeyConstraint baseColumnNames="parameter_id" baseTableName="stretchy_report_parameter"
                                 constraintName="fk_report_parameter_002" deferrable="false" initiallyDeferred="false"
                                 onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="id"
                                 referencedTableName="stretchy_parameter" validate="true"/>
    </changeSet>
</databaseChangeLog>
