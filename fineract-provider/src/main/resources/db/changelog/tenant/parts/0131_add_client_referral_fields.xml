<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements. See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership. The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied. See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="Nahom G/cherkos" id="131.1">
        <addColumn tableName="m_client">
            <column name="marital_status_cv_id" type="bigint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="Nahom G/cherkos" id="131.2">
        <addColumn tableName="m_client">
            <column name="qualification_cv_id" type="bigint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="Nahom G/cherkos" id="131.3">
        <addColumn tableName="m_client">
            <column name="profession_cv_id" type="bigint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="Nahom G/cherkos" id="131.4">
        <addColumn tableName="m_client_referral">
            <column name="staff_referral_id" type="bigint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint baseColumnNames="staff_referral_id"
                                 baseTableName="m_client_referral"
                                 constraintName="FK_m_client_referral_staff_id"
                                 deferrable="false"
                                 initiallyDeferred="false"
                                 onDelete="RESTRICT"
                                 onUpdate="RESTRICT"
                                 referencedColumnNames="id"
                                 referencedTableName="m_staff"
                                 validate="true"/>
    </changeSet>

    <changeSet author="Nahom G/cherkos" id="131.5">
        <addColumn tableName="m_client_referral">
            <column name="referral_source_description" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="Nahom G/cherkos" id="131.6">

        <addColumn tableName="m_client_referral">
            <column name="referral_type_cv_id" type="bigint">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <changeSet author="Nahom G/cherkos" id="131.8">
        <insert tableName="m_code">
            <column name="code_name" value="REFERRAL_SOURCE"/>
            <column name="is_system_defined" valueBoolean="true"/>
        </insert>
    </changeSet>

    <changeSet author="Nahom G/cherkos" id="131.9">
        <insert tableName="m_code">
            <column name="code_name" value="QUALIFICATION"/>
            <column name="is_system_defined" valueBoolean="true"/>
        </insert>
    </changeSet>

    <changeSet author="Nahom G/cherkos" id="131.10">
        <dropNotNullConstraint tableName="m_client_referral" columnName="client_referral_id" columnDataType="BIGINT"/>
    </changeSet>

    <changeSet author="Nahom G/cherkos" id="131.11">
        <addColumn tableName="m_family_members">
            <column name="qualification_cv_id" type="int(11)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addForeignKeyConstraint baseColumnNames="qualification_cv_id"
                                 baseTableName="m_family_members"
                                 constraintName="FK_m_family_members_qualification_cv_id"
                                 deferrable="false"
                                 initiallyDeferred="false"
                                 onDelete="RESTRICT"
                                 onUpdate="RESTRICT"
                                 referencedColumnNames="id"
                                 referencedTableName="m_code_value"
                                 validate="true"/>
    </changeSet>
</databaseChangeLog>