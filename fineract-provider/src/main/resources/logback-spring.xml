<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements. See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership. The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License. You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied. See the License for the
    specific language governing permissions and limitations
    under the License.

-->
<configuration>
    <springProperty name="jsonLoggingEnabled" source="fineract.logging.json.enabled" defaultValue="false"/>

    <!-- see https://docs.spring.io/spring-boot/docs/current/reference/html/spring-boot-features.html#boot-features-custom-log-configuration
         and https://github.com/spring-projects/spring-boot/tree/master/spring-boot-project/spring-boot/src/main/resources/org/springframework/boot/logging/logback -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <appender name="CONSOLE" target="System.out" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- But these three INFO are still handy ;-) just to see when it's up and running -->
    <logger name="org.springframework.boot.web.embedded.tomcat.TomcatWebServer" level="info"/>
    <logger name="org.apache.fineract.ServerApplication" level="info"/>
    <logger name="org.apache.fineract" level="${FINERACT_LOGGING_LEVEL:-INFO}"/>
    <logger name="liquibase" level="warn"/>

    <if condition="${jsonLoggingEnabled} == true">
        <then>
            <appender name="JSON" class="ch.qos.logback.core.ConsoleAppender">
                <layout class="ch.qos.logback.contrib.json.classic.JsonLayout">
                    <jsonFormatter class="ch.qos.logback.contrib.jackson.JacksonJsonFormatter">
                        <prettyPrint>false</prettyPrint>
                    </jsonFormatter>
                    <appendLineSeparator>true</appendLineSeparator>
                </layout>
            </appender>

            <root level="info">
                <appender-ref ref="JSON"/>
            </root>
        </then>
        <else>
            <!-- See https://github.com/apache/fineract/#logging-guidelines for why by default we log only to INFO, only (WARN and) ERROR
                 but it's still possible to override this using java -Dlogging.level.root=info -jar fineract-provider.jar, as per https://docs.spring.io/spring-boot/docs/current/reference/html/spring-boot-features.html#boot-features-custom-log-levels -->
            <root level="info">
                <appender-ref ref="CONSOLE"/>
            </root>
        </else>
    </if>
</configuration>
