/**
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements. See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership. The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
description = 'Fineract Integration Tests'

apply plugin: 'com.bmuschko.cargo'

// Configuration for the Gradle Cargo plugin
// https://github.com/bmuschko/gradle-cargo-plugin
configurations {
    tomcat
}

apply from: 'dependencies.gradle'

// Allow external drivers to be used for the tests without packaging it
// mainly due to license incompatibilities
configurations {
    driver
}
dependencies {
    driver 'mysql:mysql-connector-java:8.0.33'
}

cargo {
    containerId "tomcat10x"

    // looks like Cargo doesn't detect the WAR file automatically in the multi-module setup
    deployable {
        file = file("$rootDir/fineract-war/build/libs/fineract-provider.war")
        context = 'fineract-provider'
    }

    local {
        logLevel = 'low'
        outputFile = file("$buildDir/cargo/integration-tests-output.log")
        installer {
            installConfiguration = configurations.tomcat
            downloadDir = file("$buildDir/download")
            extractDir = file("$buildDir/tomcat-integration-tests")
        }
        startStopTimeout = 1200000
        sharedClasspath = configurations.driver
        containerProperties {
            def jvmArgs = '--add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.security=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.management/javax.management=ALL-UNNAMED --add-opens=java.naming/javax.naming=ALL-UNNAMED '
            if (project.hasProperty('localDebug')) {
                jvmArgs += ' -agentlib:jdwp=transport=dt_socket,server=y,address=*:9000,suspend=n -Xmx2G -Duser.timezone=Asia/Kolkata '
            }
            if (project.hasProperty('dbType')) {
                if ('postgresql'.equalsIgnoreCase(dbType)) {
                    jvmArgs += '-Dspring.datasource.hikari.driverClassName=org.postgresql.Driver -Dspring.datasource.hikari.jdbcUrl=************************************************* -Dspring.datasource.hikari.username=root -Dspring.datasource.hikari.password=postgres -Dfineract.tenant.host=localhost -Dfineract.tenant.port=5432 -Dfineract.tenant.username=root -Dfineract.tenant.password=postgres'
                } else if ('mysql'.equalsIgnoreCase(dbType)) {
                    jvmArgs += '-Dspring.datasource.hikari.driverClassName=com.mysql.cj.jdbc.Driver -Dspring.datasource.hikari.jdbcUrl=******************************************** -Dspring.datasource.hikari.username=root -Dspring.datasource.hikari.password=mysql -Dfineract.tenant.host=localhost -Dfineract.tenant.port=3306 -Dfineract.tenant.username=root -Dfineract.tenant.password=mysql'
                } else {
                    throw new GradleException('Provided dbType is not supported')
                }
            } else {
                jvmArgs += '-Dspring.datasource.hikari.driverClassName=org.mariadb.jdbc.Driver -Dspring.datasource.hikari.jdbcUrl=********************************************** -Dspring.datasource.hikari.username=root -Dspring.datasource.hikari.password=mysql -Dfineract.tenant.host=localhost -Dfineract.tenant.port=3306 -Dfineract.tenant.username=root -Dfineract.tenant.password=mysql'
            }
            jvmArgs += ' -Dspring.profiles.active=test -Dfineract.events.external.enabled=true'
            property 'cargo.start.jvmargs', jvmArgs
            property 'cargo.tomcat.connector.keystoreFile', file("$rootDir/fineract-provider/src/main/resources/keystore.jks")
            property 'cargo.tomcat.connector.keystorePass', 'openmf'
            property 'cargo.tomcat.connector.keystoreType', 'JKS'
            property 'cargo.tomcat.httpSecure', true
            property 'cargo.tomcat.connector.sslProtocol', 'TLS'
            property 'cargo.tomcat.connector.clientAuth', false
            property 'cargo.protocol', 'https'
            property 'cargo.servlet.port', 8443
        }
    }
}

cargoRunLocal.dependsOn ':fineract-war:war'
cargoStartLocal.dependsOn ':fineract-war:war'
cargoStartLocal.mustRunAfter 'testClasses'

if (!project.hasProperty('cargoDisabled')) {
    test {
        dependsOn(cargoStartLocal)
        finalizedBy cargoStopLocal
    }
}

// NOTE: Gradle suggested these dependencies
compileTestJava.dependsOn(':fineract-provider:generateGitProperties', ':fineract-provider:processResources', ':fineract-provider:resolve')
spotbugsTest.dependsOn(':fineract-provider:generateGitProperties', ':fineract-provider:processResources', ':fineract-provider:resolve')
