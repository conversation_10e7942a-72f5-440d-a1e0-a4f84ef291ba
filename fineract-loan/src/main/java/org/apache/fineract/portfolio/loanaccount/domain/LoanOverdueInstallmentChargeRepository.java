package org.apache.fineract.portfolio.loanaccount.domain;

import java.util.Collection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface LoanOverdueInstallmentChargeRepository extends JpaRepository<LoanOverdueInstallmentCharge, Long> {

    @Transactional
    @Modifying
    @Query("delete from LoanOverdueInstallmentCharge l where l.loancharge.loan.id = ?1")
    void deleteByLoanById(Long loanId);

    @Transactional
    @Modifying
    @Query("delete from LoanOverdueInstallmentCharge l where l.loancharge.loan.id = ?1 and l.installment.id not in ?2")
    void deleteByInstallmentIds(Long loanId, Collection<Long> installmentIds);

}
